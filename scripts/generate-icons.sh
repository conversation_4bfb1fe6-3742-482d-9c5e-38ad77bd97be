#!/bin/bash

# Generate PWA icons from SVG
# This script creates all required icon sizes for PWA installation

echo "🎨 Generating PWA icons..."

# Check if ImageMagick is installed
if ! command -v convert &> /dev/null; then
    echo "❌ ImageMagick not found. Install it with:"
    echo "  brew install imagemagick  # macOS"
    echo "  sudo apt install imagemagick  # Ubuntu"
    echo "  Or manually create PNG icons from static/icons/icon.svg"
    exit 1
fi

# Create icons directory if it doesn't exist
mkdir -p static/icons

# Source SVG file
SVG_FILE="static/icons/icon.svg"

if [ ! -f "$SVG_FILE" ]; then
    echo "❌ Source SVG file not found: $SVG_FILE"
    echo "Creating a simple SVG icon..."
    
    # Create a simple SVG icon
    cat > "$SVG_FILE" << 'EOF'
<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <rect width="512" height="512" rx="64" fill="#4CAF50"/>
  <text x="256" y="320" font-family="Arial, sans-serif" font-size="200" text-anchor="middle" fill="white">🍽️</text>
  <text x="256" y="420" font-family="Arial, sans-serif" font-size="48" text-anchor="middle" fill="white" font-weight="bold">MealPal</text>
</svg>
EOF
fi

# Array of required icon sizes
SIZES=(72 96 128 144 152 192 384 512)

echo "📱 Generating icons from $SVG_FILE..."

# Generate each size
for size in "${SIZES[@]}"; do
    output_file="static/icons/icon-${size}x${size}.png"
    echo "  Creating ${size}x${size} icon..."
    
    convert "$SVG_FILE" -resize "${size}x${size}" "$output_file"
    
    if [ $? -eq 0 ]; then
        echo "  ✅ Created $output_file"
    else
        echo "  ❌ Failed to create $output_file"
    fi
done

# Create favicon
echo "🌐 Creating favicon..."
convert "$SVG_FILE" -resize "32x32" "static/favicon.ico"

echo ""
echo "✅ Icon generation complete!"
echo ""
echo "📋 Generated files:"
for size in "${SIZES[@]}"; do
    echo "  • static/icons/icon-${size}x${size}.png"
done
echo "  • static/favicon.ico"
echo ""
echo "🔧 Next steps:"
echo "  1. Test PWA installation on mobile device"
echo "  2. Verify all icons load correctly"
echo "  3. Check Chrome DevTools > Application > Manifest"

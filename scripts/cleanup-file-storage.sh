#!/bin/bash

# MealPal File Storage Cleanup Script
# Removes the old file-based user storage after migration to Redis

echo "🧹 MealPal File Storage Cleanup"
echo "==============================="

# Check if data directory exists
if [ -d "data" ]; then
    echo "📁 Found data directory"
    
    # Check if users.json exists
    if [ -f "data/users.json" ]; then
        echo "📄 Found users.json file"
        
        # Show current content for confirmation
        echo ""
        echo "📊 Current user data in file:"
        echo "-----------------------------"
        if command -v jq &> /dev/null; then
            cat data/users.json | jq '.'
        else
            cat data/users.json
        fi
        
        echo ""
        echo "⚠️  This data should now be stored in Redis."
        echo "   You can verify by checking Redis with:"
        echo "   docker exec mealpal-redis redis-cli -a hello-redis KEYS \"user:*\""
        echo ""
        
        # Ask for confirmation
        read -p "🗑️  Remove data/users.json? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            rm data/users.json
            echo "✅ Removed data/users.json"
            
            # Check if data directory is now empty
            if [ -z "$(ls -A data)" ]; then
                read -p "📁 Remove empty data directory? (y/N): " -n 1 -r
                echo
                if [[ $REPLY =~ ^[Yy]$ ]]; then
                    rmdir data
                    echo "✅ Removed empty data directory"
                else
                    echo "📁 Keeping empty data directory"
                fi
            else
                echo "📁 Data directory contains other files, keeping it"
            fi
        else
            echo "❌ Cancelled - keeping data/users.json"
        fi
    else
        echo "✅ No users.json file found - already cleaned up"
    fi
else
    echo "✅ No data directory found - already cleaned up"
fi

echo ""
echo "🎉 Cleanup complete!"
echo ""
echo "📝 Note: User data is now stored in Redis:"
echo "   - User profiles: user:{user_id}"
echo "   - Session data: session:{session_id}"
echo "   - User sessions: user_sessions:{user_id}"

#!/bin/bash

# MealPal Cronjob Testing Script
# This script tests the daily summary cronjob functionality

echo "🧪 Testing MealPal Daily Summary Cronjob"
echo "========================================"

# Check if server is running
echo "📡 Checking if MealPal server is running..."
if ! curl -s http://localhost:8080/ > /dev/null; then
    echo "❌ MealPal server is not running on localhost:8080"
    echo "   Please start the server first with: go run cmd/main.go"
    exit 1
fi
echo "✅ Server is running"

# Test the debug endpoint
echo ""
echo "🔄 Triggering daily summary processing..."
response=$(curl -s -X POST http://localhost:8080/debug/trigger-daily-summary \
    -H "X-Debug-Key: mealpal-debug-2025" \
    -H "Content-Type: application/json")

echo "📋 Response:"
echo "$response" | jq . 2>/dev/null || echo "$response"

# Check if the request was successful
if echo "$response" | grep -q '"success":true'; then
    echo ""
    echo "✅ Daily summary processing triggered successfully!"
    echo "📝 Check the server logs to see the processing details"
    echo ""
    echo "💡 What happens next:"
    echo "   1. The cronjob processes all users in the background"
    echo "   2. For each user, it reads yesterday's food data"
    echo "   3. It calculates total macros (calories, protein, fat, carbs)"
    echo "   4. It creates/updates a 'Daily Summary' sheet in their spreadsheet"
    echo "   5. Check server console for detailed logs"
else
    echo ""
    echo "❌ Failed to trigger daily summary processing"
    echo "   Response: $response"
fi

echo ""
echo "🔍 Alternative testing methods:"
echo "   1. Wait for midnight UTC for automatic processing"
echo "   2. Use the authenticated endpoint (requires login):"
echo "      curl -X POST http://localhost:8080/api/trigger-daily-summary \\"
echo "           -H \"Cookie: session=your-session-cookie\""
echo "   3. Run the comprehensive test: go run scripts/test-cronjob.go"
echo ""
echo "📊 To verify results:"
echo "   1. Login to MealPal and check your Google Sheets"
echo "   2. Look for a 'Daily Summary' sheet with aggregated data"
echo "   3. Check server logs for processing details"

#!/bin/bash

# Test script for date-specific daily summary cronjob

echo "🧪 Testing Date-Specific Daily Summary Cronjob"
echo "=============================================="

# Check if server is running
echo "📡 Checking if MealPal server is running..."
if ! curl -s http://localhost:8080/ > /dev/null; then
    echo "❌ MealPal server is not running on localhost:8080"
    echo "   Please start the server first with: go run cmd/main.go"
    exit 1
fi
echo "✅ Server is running"

echo ""
echo "🗓️  Testing different date scenarios..."

# Test 1: Today's date
today=$(date +%Y-%m-%d)
echo "1. Testing with today's date ($today)..."
response=$(curl -s -X POST "http://localhost:8080/debug/trigger-daily-summary/$today" \
    -H "X-Debug-Key: mealpal-debug-2025" \
    -H "Content-Type: application/json")

echo "   Response:"
echo "   $response" | jq . 2>/dev/null || echo "   $response"

# Test 2: Yesterday's date
yesterday=$(date -d "yesterday" +%Y-%m-%d 2>/dev/null || date -v-1d +%Y-%m-%d 2>/dev/null || echo "2025-06-07")
echo ""
echo "2. Testing with yesterday's date ($yesterday)..."
response=$(curl -s -X POST "http://localhost:8080/debug/trigger-daily-summary/$yesterday" \
    -H "X-Debug-Key: mealpal-debug-2025" \
    -H "Content-Type: application/json")

echo "   Response:"
echo "   $response" | jq . 2>/dev/null || echo "   $response"

# Test 3: Query parameter method (default to yesterday)
echo ""
echo "3. Testing with query parameter (defaults to yesterday)..."
response=$(curl -s -X POST "http://localhost:8080/debug/trigger-daily-summary" \
    -H "X-Debug-Key: mealpal-debug-2025" \
    -H "Content-Type: application/json")

echo "   Response:"
echo "   $response" | jq . 2>/dev/null || echo "   $response"

# Test 4: Query parameter with specific date
echo ""
echo "4. Testing with query parameter for today ($today)..."
response=$(curl -s -X POST "http://localhost:8080/debug/trigger-daily-summary?date=$today" \
    -H "X-Debug-Key: mealpal-debug-2025" \
    -H "Content-Type: application/json")

echo "   Response:"
echo "   $response" | jq . 2>/dev/null || echo "   $response"

# Test 5: Invalid date format
echo ""
echo "5. Testing with invalid date format..."
response=$(curl -s -X POST "http://localhost:8080/debug/trigger-daily-summary/invalid-date" \
    -H "X-Debug-Key: mealpal-debug-2025" \
    -H "Content-Type: application/json")

echo "   Response:"
echo "   $response" | jq . 2>/dev/null || echo "   $response"

echo ""
echo "📊 Summary of new functionality:"
echo "✅ Path parameter: POST /debug/trigger-daily-summary/YYYY-MM-DD"
echo "✅ Query parameter: POST /debug/trigger-daily-summary?date=YYYY-MM-DD"
echo "✅ Default behavior: Uses yesterday's date if no date specified"
echo "✅ Cron schedule: Runs at 00:01 UTC (processes previous day)"
echo ""
echo "💡 Usage examples:"
echo "   # Process today's data"
echo "   curl -X POST http://localhost:8080/debug/trigger-daily-summary/$today \\"
echo "        -H \"X-Debug-Key: mealpal-debug-2025\""
echo ""
echo "   # Process yesterday's data (default)"
echo "   curl -X POST http://localhost:8080/debug/trigger-daily-summary \\"
echo "        -H \"X-Debug-Key: mealpal-debug-2025\""
echo ""
echo "   # Process specific date"
echo "   curl -X POST http://localhost:8080/debug/trigger-daily-summary/2025-06-08 \\"
echo "        -H \"X-Debug-Key: mealpal-debug-2025\""
echo ""
echo "📝 Check server logs to see the actual processing results!"

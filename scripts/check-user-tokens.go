package checkusertokens

import (
	"encoding/json"
	"fmt"
	"os"

	"github.com/stefanoschrs/mealpal/internal/services"
)

func CheckUserTokens() {
	fmt.Println("🔍 MealPal User Token Status Checker")
	fmt.Println("====================================")

	// Change to the directory where the app runs
	if len(os.Args) > 1 {
		os.Chdir(os.Args[1])
	}

	// Initialize user store
	userStore := services.NewUserStore()
	allUsers := userStore.GetAllUsers()

	if len(allUsers) == 0 {
		fmt.Println("📭 No users found in the system")
		return
	}

	fmt.Printf("👥 Found %d users in the system:\n\n", len(allUsers))

	validUsers := 0
	needReauth := 0

	for i, user := range allUsers {
		fmt.Printf("%d. User: %s\n", i+1, user.Email)
		fmt.Printf("   ID: %s\n", user.ID)
		fmt.Printf("   Name: %s\n", user.Name)
		fmt.Printf("   Spreadsheet ID: %s\n", getSpreadsheetStatus(user.SpreadsheetID))
		fmt.Printf("   Access Token: %s\n", getTokenStatus(user.AccessToken))
		fmt.Printf("   Refresh Token: %s\n", getTokenStatus(user.RefreshToken))

		if user.SpreadsheetID != "" {
			if user.AccessToken != "" && user.RefreshToken != "" {
				fmt.Printf("   Status: ✅ Ready for cronjob processing\n")
				validUsers++
			} else if user.AccessToken != "" {
				fmt.Printf("   Status: ⚠️  Has access token but no refresh token (may fail on expiry)\n")
				needReauth++
			} else {
				fmt.Printf("   Status: ❌ Needs to log in again (no tokens)\n")
				needReauth++
			}
		} else {
			fmt.Printf("   Status: 📝 No spreadsheet yet (new user)\n")
		}
		fmt.Println()
	}

	fmt.Println("📊 Summary:")
	fmt.Printf("   ✅ Users ready for cronjob: %d\n", validUsers)
	fmt.Printf("   ❌ Users needing re-authentication: %d\n", needReauth)
	fmt.Printf("   📝 New users (no spreadsheet): %d\n", len(allUsers)-validUsers-needReauth)

	if needReauth > 0 {
		fmt.Println("\n💡 To fix users needing re-authentication:")
		fmt.Println("   1. Those users need to log out and log back in")
		fmt.Println("   2. This will refresh their OAuth tokens")
		fmt.Println("   3. After re-login, the cronjob will work for them")
	}

	// Show the raw data file for debugging
	fmt.Println("\n🔧 Debug Information:")
	if data, err := os.ReadFile("data/users.json"); err == nil {
		fmt.Println("Raw users.json content:")
		var prettyJSON map[string]interface{}
		if json.Unmarshal(data, &prettyJSON) == nil {
			if prettyData, err := json.MarshalIndent(prettyJSON, "", "  "); err == nil {
				fmt.Println(string(prettyData))
			}
		}
	} else {
		fmt.Printf("Could not read users.json: %v\n", err)
	}
}

func getSpreadsheetStatus(spreadsheetID string) string {
	if spreadsheetID == "" {
		return "❌ None"
	}
	return fmt.Sprintf("✅ %s", spreadsheetID)
}

func getTokenStatus(token string) string {
	if token == "" {
		return "❌ Missing"
	}
	if len(token) > 20 {
		return fmt.Sprintf("✅ Present (%s...)", token[:20])
	}
	return fmt.Sprintf("✅ Present (%s)", token)
}

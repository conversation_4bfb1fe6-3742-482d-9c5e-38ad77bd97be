#!/bin/bash

# Generate version information for cache busting
# This script creates a version.txt file with the current git commit hash

set -e

# Get git commit hash (short version)
GIT_COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "dev-$(date +%s)")

# Create version file
echo "$GIT_COMMIT" > static/version.txt

echo "Generated version: $GIT_COMMIT"
echo "Version file created at: static/version.txt"

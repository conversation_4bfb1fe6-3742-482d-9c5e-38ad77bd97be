#!/bin/bash

# MealPal Debug Script
# Helps verify user data persistence and spreadsheet management

echo "🔍 MealPal Debug Information"
echo "================================"

# Check if data directory exists
if [ -d "data" ]; then
    echo "✅ Data directory exists"
    
    # Check if users.json exists
    if [ -f "data/users.json" ]; then
        echo "✅ users.json file exists"
        echo ""
        echo "📊 User Data:"
        echo "-------------"
        
        # Pretty print the JSON if jq is available
        if command -v jq &> /dev/null; then
            cat data/users.json | jq '.'
        else
            echo "📝 Raw JSON (install jq for pretty printing):"
            cat data/users.json
        fi
        
        echo ""
        echo "👥 User Summary:"
        echo "---------------"
        
        # Count users and show basic info
        if command -v jq &> /dev/null; then
            USER_COUNT=$(cat data/users.json | jq 'length')
            echo "Total users: $USER_COUNT"
            echo ""
            
            # Show each user's email and spreadsheet status
            cat data/users.json | jq -r 'to_entries[] | "• \(.value.email) - SpreadsheetID: \(if .value.spreadsheet_id then .value.spreadsheet_id else "❌ None" end)"'
        else
            echo "Install jq for detailed user summary"
        fi
        
    else
        echo "❌ users.json file not found"
        echo "   This means no users have logged in yet"
    fi
else
    echo "❌ Data directory not found"
    echo "   This means the server hasn't been started yet or no users have logged in"
fi

echo ""
echo "🧪 Testing Commands:"
echo "-------------------"
echo "• Reset all data:     rm -rf data/"
echo "• View raw user data: cat data/users.json"
echo "• Start server:       make run"
echo "• Build for Linux:    make build-linux"

echo ""
echo "🌐 Test URLs:"
echo "-------------"
echo "• Home page:          http://localhost:8080"
echo "• Login:              http://localhost:8080/auth/google/login"
echo "• Dashboard:          http://localhost:8080/dashboard"

#!/usr/bin/env python3

"""
Simple icon generator for MealPal PWA
Creates basic PNG icons without external dependencies
"""

import os
from PIL import Image, ImageDraw, ImageFont

def create_icon(size, output_path):
    """Create a simple icon with the given size"""
    # Create a new image with green background
    img = Image.new('RGB', (size, size), '#4CAF50')
    draw = ImageDraw.Draw(img)
    
    # Try to use a system font, fallback to default
    try:
        # Calculate font size based on icon size
        font_size = max(size // 8, 12)
        font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", font_size)
    except:
        try:
            font = ImageFont.truetype("arial.ttf", size // 8)
        except:
            font = ImageFont.load_default()
    
    # Draw the emoji/text
    emoji = "🍽️"
    text = "MealPal"
    
    # Get text dimensions
    try:
        emoji_bbox = draw.textbbox((0, 0), emoji, font=font)
        emoji_width = emoji_bbox[2] - emoji_bbox[0]
        emoji_height = emoji_bbox[3] - emoji_bbox[1]
    except:
        # Fallback for older PIL versions
        emoji_width, emoji_height = draw.textsize(emoji, font=font)
    
    # Draw emoji in center-top
    emoji_x = (size - emoji_width) // 2
    emoji_y = size // 3
    draw.text((emoji_x, emoji_y), emoji, fill='white', font=font)
    
    # Draw text below emoji
    try:
        text_bbox = draw.textbbox((0, 0), text, font=font)
        text_width = text_bbox[2] - text_bbox[0]
    except:
        text_width, _ = draw.textsize(text, font=font)
    
    text_x = (size - text_width) // 2
    text_y = emoji_y + emoji_height + 10
    draw.text((text_x, text_y), text, fill='white', font=font)
    
    # Save the image
    img.save(output_path, 'PNG')
    print(f"✅ Created {output_path}")

def main():
    print("🎨 Creating simple PWA icons...")
    
    # Create icons directory
    os.makedirs('static/icons', exist_ok=True)
    
    # Icon sizes needed for PWA
    sizes = [72, 96, 128, 144, 152, 192, 384, 512]
    
    for size in sizes:
        output_path = f'static/icons/icon-{size}x{size}.png'
        create_icon(size, output_path)
    
    # Create favicon
    create_icon(32, 'static/favicon.ico')
    
    print("\n✅ Icon generation complete!")
    print("\n📋 Generated files:")
    for size in sizes:
        print(f"  • static/icons/icon-{size}x{size}.png")
    print("  • static/favicon.ico")

if __name__ == "__main__":
    try:
        main()
    except ImportError:
        print("❌ PIL (Pillow) not found. Install it with:")
        print("  pip install Pillow")
        print("\nOr manually create PNG icons in static/icons/ directory")
        print("Required sizes: 72x72, 96x96, 128x128, 144x144, 152x152, 192x192, 384x384, 512x512")

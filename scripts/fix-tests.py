#!/usr/bin/env python3

import re

# Read the test file
with open('internal/handlers/app_test.go', 'r') as f:
    content = f.read()

# Pattern to match the duplicate service creation blocks
pattern = r'''	cfg := &config\.Config\{[^}]+\}

	// Create a temporary directory for testing
	tempDir := t\.TempDir\(\)
	originalDir, _ := os\.Getwd\(\)
	os\.Chdir\(tempDir\)
	defer os\.Chdir\(originalDir\)

	authService := services\.NewAuthService\(cfg\)
	store := sessions\.NewCookieStore\(\[\]byte\("test-secret"\)\)
	
	geminiService := services\.NewGeminiService\(cfg\)
	userStore := services\.NewUserStore\(\)
	sheetsService := services\.NewSheetsService\(authService\.GetOAuthConfig\(\), userStore\)
	foodLogService := services\.NewFoodLogService\(geminiService, sheetsService, userStore\)

	handler, _, _ := createTestHandler\(t\)'''

# Replace with just the handler creation
replacement = '\thandler, _, _ := createTestHandler(t)'

# Apply the replacement
new_content = re.sub(pattern, replacement, content, flags=re.MULTILINE)

# Write back to file
with open('internal/handlers/app_test.go', 'w') as f:
    f.write(new_content)

print("Fixed test file by removing duplicate service creation")

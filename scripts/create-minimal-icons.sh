#!/bin/bash

# Create minimal PNG icons for PWA
# This creates simple green square icons without external dependencies

echo "🎨 Creating minimal PWA icons..."

# Create icons directory
mkdir -p static/icons

# Base64 encoded 1x1 green PNG
GREEN_PNG_BASE64="iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg=="

# Function to create a simple colored PNG
create_icon() {
    local size=$1
    local output=$2
    
    # Create a simple SVG and convert to PNG using built-in tools
    cat > "/tmp/icon_${size}.svg" << EOF
<svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">
  <rect width="${size}" height="${size}" fill="#4CAF50"/>
  <text x="50%" y="40%" font-family="Arial, sans-serif" font-size="$((size/8))" text-anchor="middle" fill="white" dominant-baseline="middle">🍽️</text>
  <text x="50%" y="70%" font-family="Arial, sans-serif" font-size="$((size/16))" text-anchor="middle" fill="white" dominant-baseline="middle" font-weight="bold">MealPal</text>
</svg>
EOF

    # Try to convert SVG to PNG using available tools
    if command -v rsvg-convert &> /dev/null; then
        rsvg-convert -w ${size} -h ${size} "/tmp/icon_${size}.svg" > "$output"
        echo "✅ Created $output (${size}x${size}) using rsvg-convert"
    elif command -v inkscape &> /dev/null; then
        inkscape "/tmp/icon_${size}.svg" --export-png="$output" --export-width=${size} --export-height=${size} &> /dev/null
        echo "✅ Created $output (${size}x${size}) using inkscape"
    elif command -v convert &> /dev/null; then
        convert "/tmp/icon_${size}.svg" -resize "${size}x${size}" "$output"
        echo "✅ Created $output (${size}x${size}) using ImageMagick"
    else
        # Fallback: create a simple colored square using base64
        # This creates a minimal valid PNG file
        echo "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==" | base64 -d > "$output"
        echo "⚠️  Created minimal $output (fallback - install rsvg-convert, inkscape, or imagemagick for better icons)"
    fi
    
    # Clean up temp file
    rm -f "/tmp/icon_${size}.svg"
}

# Create all required icon sizes
sizes=(72 96 128 144 152 192 384 512)

for size in "${sizes[@]}"; do
    create_icon $size "static/icons/icon-${size}x${size}.png"
done

# Create favicon
create_icon 32 "static/favicon.ico"

echo ""
echo "✅ Icon generation complete!"
echo ""
echo "📋 Generated files:"
for size in "${sizes[@]}"; do
    echo "  • static/icons/icon-${size}x${size}.png"
done
echo "  • static/favicon.ico"
echo ""
echo "🔧 For better quality icons, install one of:"
echo "  • rsvg-convert: brew install librsvg"
echo "  • inkscape: brew install inkscape"  
echo "  • imagemagick: brew install imagemagick"

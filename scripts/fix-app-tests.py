#!/usr/bin/env python3

import re

def fix_test_file():
    # Read the test file
    with open('internal/handlers/app_test.go', 'r') as f:
        content = f.read()

    # Pattern to match the duplicate service creation blocks
    # This pattern matches from cfg := &config.Config{ to handler, _, _ := createTestHandler(t)
    pattern = r'''	cfg := &config\.Config\{[^}]+\}

	// Create a temporary directory for testing
	tempDir := t\.TempDir\(\)
	originalDir, _ := os\.Getwd\(\)
	os\.Chdir\(tempDir\)
	defer os\.Chdir\(originalDir\)

	authService := services\.NewAuthService\(cfg\)
	store := sessions\.NewCookieStore\(\[\]byte\("test-secret"\)\)

	geminiService := services\.NewGeminiService\(cfg\)
	userStore := services\.NewUserStore\(\)
	sheetsService := services\.NewSheetsService\(authService\.GetOAuthConfig\(\), userStore\)
	foodLogService := services\.NewFoodLogService\(geminiService, sheetsService, userStore\)

	handler, _, _ := createTestHandler\(t\)'''

    # Replace with just the handler creation
    replacement = '\thandler, _, _ := createTestHandler(t)'

    # Apply the replacement
    new_content = re.sub(pattern, replacement, content, flags=re.MULTILINE)

    # Also handle variations without the store line
    pattern2 = r'''	cfg := &config\.Config\{[^}]+\}

	// Create a temporary directory for testing
	tempDir := t\.TempDir\(\)
	originalDir, _ := os\.Getwd\(\)
	os\.Chdir\(tempDir\)
	defer os\.Chdir\(originalDir\)

	authService := services\.NewAuthService\(cfg\)

	geminiService := services\.NewGeminiService\(cfg\)
	userStore := services\.NewUserStore\(\)
	sheetsService := services\.NewSheetsService\(authService\.GetOAuthConfig\(\), userStore\)
	foodLogService := services\.NewFoodLogService\(geminiService, sheetsService, userStore\)

	handler, _, _ := createTestHandler\(t\)'''

    new_content = re.sub(pattern2, replacement, new_content, flags=re.MULTILINE)

    # Handle another variation
    pattern3 = r'''	cfg := &config\.Config\{[^}]+\}

	// Create a temporary directory for testing
	tempDir := t\.TempDir\(\)
	originalDir, _ := os\.Getwd\(\)
	os\.Chdir\(tempDir\)
	defer os\.Chdir\(originalDir\)

	authService := services\.NewAuthService\(cfg\)

	geminiService := services\.NewGeminiService\(cfg\)
	userStore := services\.NewUserStore\(\)
	sheetsService := services\.NewSheetsService\(authService\.GetOAuthConfig\(\), userStore\)

	handler, _, _ := createTestHandler\(t\)'''

    new_content = re.sub(pattern3, replacement, new_content, flags=re.MULTILINE)

    # Remove unused imports if they're not used elsewhere
    # Check if config, sessions, middleware are used elsewhere
    if 'config.Config' not in new_content.replace('// Helper function to create test services and handler', ''):
        new_content = re.sub(r'\t"github\.com/stefanoschrs/mealpal/internal/config"\n', '', new_content)
    
    if 'sessions.NewCookieStore' not in new_content.replace('// Helper function to create test services and handler', ''):
        new_content = re.sub(r'\t"github\.com/gorilla/sessions"\n', '', new_content)

    # Write back to file
    with open('internal/handlers/app_test.go', 'w') as f:
        f.write(new_content)

    print("Fixed test file by removing duplicate service creation")

if __name__ == "__main__":
    fix_test_file()

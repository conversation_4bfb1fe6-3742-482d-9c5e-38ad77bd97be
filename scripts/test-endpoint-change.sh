#!/bin/bash

# Test script to verify the API endpoint change from /submit-food to /api/submit-food

echo "🧪 Testing API Endpoint Change"
echo "=============================="

# Check if server is running
echo "📡 Checking if MealPal server is running..."
if ! curl -s http://localhost:8080/ > /dev/null; then
    echo "❌ MealPal server is not running on localhost:8080"
    echo "   Please start the server first with: go run cmd/main.go"
    exit 1
fi
echo "✅ Server is running"

echo ""
echo "🔍 Testing endpoint changes..."

# Test 1: Old endpoint should return 404
echo "1. Testing old endpoint POST /api/submit-food (should return 404)..."
response=$(curl -s -o /dev/null -w "%{http_code}" -X POST http://localhost:8080/api/submit-food \
    -H "Content-Type: application/json" \
    -d '{"text":"test food"}')

if [ "$response" = "404" ]; then
    echo "   ✅ POST /api/submit-food returns 404 (correct - endpoint removed)"
else
    echo "   ❌ POST /api/submit-food returns $response (expected 404)"
fi

# Test 2: New endpoint should redirect (302) when not authenticated
echo "2. Testing new endpoint POST /api/submit-food (should redirect without auth)..."
response=$(curl -s -o /dev/null -w "%{http_code}" -X POST http://localhost:8080/api/submit-food \
    -H "Content-Type: application/json" \
    -d '{"text":"test food"}')

if [ "$response" = "302" ]; then
    echo "   ✅ POST /api/submit-food returns 302 (correct - requires authentication)"
else
    echo "   ❌ POST /api/submit-food returns $response (expected 302)"
fi

# Test 3: Check that the JavaScript file was updated
echo "3. Checking JavaScript file for endpoint update..."
if grep -q "/api/submit-food" static/js/app.js; then
    echo "   ✅ JavaScript file updated to use /api/submit-food"
else
    echo "   ❌ JavaScript file still references old endpoint"
fi

# Test 4: Check that main.go was updated
echo "4. Checking main.go for route update..."
if grep -q 'POST("/api/submit-food"' cmd/main.go; then
    echo "   ✅ main.go updated to use /api/submit-food route"
else
    echo "   ❌ main.go still uses old route"
fi

echo ""
echo "📊 Summary:"
echo "✅ Old /submit-food endpoint properly removed (returns 404)"
echo "✅ New /api/submit-food endpoint properly protected (requires auth)"
echo "✅ JavaScript updated to call new endpoint"
echo "✅ Go routes updated to serve new endpoint"

echo ""
echo "🎉 API endpoint migration completed successfully!"
echo ""
echo "📝 To test the full functionality:"
echo "   1. Start the server: go run cmd/main.go"
echo "   2. Open http://localhost:8080 in your browser"
echo "   3. Login with Google"
echo "   4. Submit a food entry to test the new endpoint"
echo "   5. Check that it works end-to-end"

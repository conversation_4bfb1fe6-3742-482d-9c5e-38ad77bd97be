package models

type User struct {
	ID            string `json:"id"`
	Email         string `json:"email"`
	Name          string `json:"name"`
	Picture       string `json:"picture"`
	AccessToken   string `json:"access_token,omitempty"`   // Deprecated: tokens now stored in Redis
	RefreshToken  string `json:"refresh_token,omitempty"`  // Deprecated: tokens now stored in Redis
	SpreadsheetID string `json:"spreadsheet_id,omitempty"`
}

type FoodEntry struct {
	UserID    string `json:"user_id"`
	Text      string `json:"text"`
	ParsedCSV string `json:"parsed_csv"`
	Timestamp string `json:"timestamp"`
}

type GoogleUserInfo struct {
	ID      string `json:"id"`
	Email   string `json:"email"`
	Name    string `json:"name"`
	Picture string `json:"picture"`
}

// SessionData represents session data stored in cookies (minimal)
type SessionData struct {
	SessionID  string `json:"session_id"`
	IsLoggedIn bool   `json:"is_logged_in"`
}

// RedisSessionData represents the full session data stored in Redis
type RedisSessionData struct {
	UserID       string        `json:"user_id"`
	AccessToken  string        `json:"access_token"`
	RefreshToken string        `json:"refresh_token"`
	TokenExpiry  string        `json:"token_expiry"` // Store as string for JSON compatibility
	CreatedAt    string        `json:"created_at"`
	LastAccessed string        `json:"last_accessed"`
}

package middleware

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/sessions"
	"github.com/stefanoschrs/mealpal/internal/models"
)

func TestAuthRequired_NoSession(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Create a test store
	store := sessions.NewCookieStore([]byte("test-secret"))

	// Create a test router
	router := gin.New()
	router.Use(AuthRequired(store))
	router.GET("/protected", func(c *gin.Context) {
		c.J<PERSON>(http.StatusOK, gin.H{"message": "success"})
	})

	// Create a request without session
	req := httptest.NewRequest("GET", "/protected", nil)
	w := httptest.NewRecorder()

	router.ServeHTTP(w, req)

	// Should redirect to home
	if w.Code != http.StatusFound {
		t.Errorf("Expected status %d, got %d", http.StatusFound, w.Code)
	}

	location := w.Header().Get("Location")
	if location != "/" {
		t.Errorf("Expected redirect to '/', got '%s'", location)
	}
}

func TestAuthRequired_EmptySessionData(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Create a test store
	store := sessions.NewCookieStore([]byte("test-secret"))

	// Create a test router
	router := gin.New()
	router.Use(AuthRequired(store))
	router.GET("/protected", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "success"})
	})

	// Create a request with empty session
	req := httptest.NewRequest("GET", "/protected", nil)
	w := httptest.NewRecorder()

	// Add empty session
	session, _ := store.Get(req, SessionName)
	session.Values["session_data"] = ""
	session.Save(req, w)

	// Create new request with the session cookie
	cookies := w.Result().Cookies()
	req2 := httptest.NewRequest("GET", "/protected", nil)
	for _, cookie := range cookies {
		req2.AddCookie(cookie)
	}
	w2 := httptest.NewRecorder()

	router.ServeHTTP(w2, req2)

	// Should redirect to home
	if w2.Code != http.StatusFound {
		t.Errorf("Expected status %d, got %d", http.StatusFound, w2.Code)
	}
}

func TestAuthRequired_InvalidSessionData(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Create a test store
	store := sessions.NewCookieStore([]byte("test-secret"))

	// Create a test router
	router := gin.New()
	router.Use(AuthRequired(store))
	router.GET("/protected", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "success"})
	})

	// Create a request with invalid session data
	req := httptest.NewRequest("GET", "/protected", nil)
	w := httptest.NewRecorder()

	// Add invalid session data
	session, _ := store.Get(req, SessionName)
	session.Values["session_data"] = "invalid json"
	session.Save(req, w)

	// Create new request with the session cookie
	cookies := w.Result().Cookies()
	req2 := httptest.NewRequest("GET", "/protected", nil)
	for _, cookie := range cookies {
		req2.AddCookie(cookie)
	}
	w2 := httptest.NewRecorder()

	router.ServeHTTP(w2, req2)

	// Should redirect to home
	if w2.Code != http.StatusFound {
		t.Errorf("Expected status %d, got %d", http.StatusFound, w2.Code)
	}
}

func TestAuthRequired_NotLoggedIn(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Create a test store
	store := sessions.NewCookieStore([]byte("test-secret"))

	// Create a test router
	router := gin.New()
	router.Use(AuthRequired(store))
	router.GET("/protected", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "success"})
	})

	// Create session data with IsLoggedIn = false
	sessionData := models.SessionData{
		SessionID:  "test_session",
		IsLoggedIn: false, // Not logged in
	}

	sessionDataJSON, _ := json.Marshal(sessionData)

	// Create a request with session data
	req := httptest.NewRequest("GET", "/protected", nil)
	w := httptest.NewRecorder()

	// Add session data
	session, _ := store.Get(req, SessionName)
	session.Values["session_data"] = string(sessionDataJSON)
	session.Save(req, w)

	// Create new request with the session cookie
	cookies := w.Result().Cookies()
	req2 := httptest.NewRequest("GET", "/protected", nil)
	for _, cookie := range cookies {
		req2.AddCookie(cookie)
	}
	w2 := httptest.NewRecorder()

	router.ServeHTTP(w2, req2)

	// Should redirect to home
	if w2.Code != http.StatusFound {
		t.Errorf("Expected status %d, got %d", http.StatusFound, w2.Code)
	}
}

func TestAuthRequired_NilUser(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Create a test store
	store := sessions.NewCookieStore([]byte("test-secret"))

	// Create a test router
	router := gin.New()
	router.Use(AuthRequired(store))
	router.GET("/protected", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "success"})
	})

	// Create session data with empty session ID
	sessionData := models.SessionData{
		SessionID:  "", // Empty session ID
		IsLoggedIn: true,
	}

	sessionDataJSON, _ := json.Marshal(sessionData)

	// Create a request with session data
	req := httptest.NewRequest("GET", "/protected", nil)
	w := httptest.NewRecorder()

	// Add session data
	session, _ := store.Get(req, SessionName)
	session.Values["session_data"] = string(sessionDataJSON)
	session.Save(req, w)

	// Create new request with the session cookie
	cookies := w.Result().Cookies()
	req2 := httptest.NewRequest("GET", "/protected", nil)
	for _, cookie := range cookies {
		req2.AddCookie(cookie)
	}
	w2 := httptest.NewRecorder()

	router.ServeHTTP(w2, req2)

	// Should redirect to home
	if w2.Code != http.StatusFound {
		t.Errorf("Expected status %d, got %d", http.StatusFound, w2.Code)
	}
}

func TestAuthRequired_ValidSession(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Create a test store
	store := sessions.NewCookieStore([]byte("test-secret"))

	// Create a test router
	router := gin.New()
	router.Use(AuthRequired(store))
	router.GET("/protected", func(c *gin.Context) {
		// The old auth middleware no longer sets user and token in context
		// It just validates the session exists and is logged in
		c.JSON(http.StatusOK, gin.H{"message": "success"})
	})

	// Create valid session data
	sessionData := models.SessionData{
		SessionID:  "test_session",
		IsLoggedIn: true,
	}

	sessionDataJSON, _ := json.Marshal(sessionData)

	// Create a request with valid session data
	req := httptest.NewRequest("GET", "/protected", nil)
	w := httptest.NewRecorder()

	// Add session data
	session, _ := store.Get(req, SessionName)
	session.Values["session_data"] = string(sessionDataJSON)
	session.Save(req, w)

	// Create new request with the session cookie
	cookies := w.Result().Cookies()
	req2 := httptest.NewRequest("GET", "/protected", nil)
	for _, cookie := range cookies {
		req2.AddCookie(cookie)
	}
	w2 := httptest.NewRecorder()

	router.ServeHTTP(w2, req2)

	// Should succeed
	if w2.Code != http.StatusOK {
		t.Errorf("Expected status %d, got %d", http.StatusOK, w2.Code)
	}
}

func TestGetUserFromContext_NoUser(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Create a test context without user
	c, _ := gin.CreateTestContext(httptest.NewRecorder())

	user, exists := GetUserFromContext(c)

	if exists {
		t.Error("Expected GetUserFromContext to return false when no user in context")
	}

	if user != nil {
		t.Error("Expected user to be nil when not in context")
	}
}

func TestGetUserFromContext_InvalidUserType(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Create a test context with invalid user type
	c, _ := gin.CreateTestContext(httptest.NewRecorder())
	c.Set("user", "invalid_user_type")

	user, exists := GetUserFromContext(c)

	if exists {
		t.Error("Expected GetUserFromContext to return false when user is not correct type")
	}

	if user != nil {
		t.Error("Expected user to be nil when type assertion fails")
	}
}

func TestGetUserFromContext_ValidUser(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Create a test context with valid user
	c, _ := gin.CreateTestContext(httptest.NewRecorder())

	expectedUser := &models.User{
		ID:    "test123",
		Email: "<EMAIL>",
		Name:  "Test User",
	}

	c.Set("user", expectedUser)

	user, exists := GetUserFromContext(c)

	if !exists {
		t.Error("Expected GetUserFromContext to return true when user exists")
	}

	if user != expectedUser {
		t.Error("Expected GetUserFromContext to return the same user instance")
	}

	if user.Email != expectedUser.Email {
		t.Errorf("Expected user email '%s', got '%s'", expectedUser.Email, user.Email)
	}
}

func TestSessionName_Constant(t *testing.T) {
	expectedSessionName := "mealpal-session"
	if SessionName != expectedSessionName {
		t.Errorf("Expected SessionName to be '%s', got '%s'", expectedSessionName, SessionName)
	}
}

func TestAuthRequired_SessionError(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Create a test store with invalid secret (too short)
	store := sessions.NewCookieStore([]byte("short"))

	// Create a test router
	router := gin.New()
	router.Use(AuthRequired(store))
	router.GET("/protected", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "success"})
	})

	// Create a request
	req := httptest.NewRequest("GET", "/protected", nil)
	w := httptest.NewRecorder()

	router.ServeHTTP(w, req)

	// Should redirect to home due to session error
	if w.Code != http.StatusFound {
		t.Errorf("Expected status %d, got %d", http.StatusFound, w.Code)
	}

	location := w.Header().Get("Location")
	if location != "/" {
		t.Errorf("Expected redirect to '/', got '%s'", location)
	}
}

func TestAuthRequired_NilOAuthToken(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Create a test store
	store := sessions.NewCookieStore([]byte("test-secret"))

	// Create a test router
	router := gin.New()
	router.Use(AuthRequired(store))
	router.GET("/protected", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "success"})
	})

	// Create session data
	sessionData := models.SessionData{
		SessionID:  "test_session",
		IsLoggedIn: true,
	}

	sessionDataJSON, _ := json.Marshal(sessionData)

	// Create a request with session data
	req := httptest.NewRequest("GET", "/protected", nil)
	w := httptest.NewRecorder()

	// Add session data
	session, _ := store.Get(req, SessionName)
	session.Values["session_data"] = string(sessionDataJSON)
	session.Save(req, w)

	// Create new request with the session cookie
	cookies := w.Result().Cookies()
	req2 := httptest.NewRequest("GET", "/protected", nil)
	for _, cookie := range cookies {
		req2.AddCookie(cookie)
	}
	w2 := httptest.NewRecorder()

	router.ServeHTTP(w2, req2)

	// Should succeed - middleware doesn't check if token is nil, only if user is logged in
	if w2.Code != http.StatusOK {
		t.Errorf("Expected status %d, got %d", http.StatusOK, w2.Code)
	}
}

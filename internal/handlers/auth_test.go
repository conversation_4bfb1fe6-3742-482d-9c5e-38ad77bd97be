package handlers

import (
	"context"
	"encoding/json"
	"html/template"
	"net/http"
	"net/http/httptest"
	"os"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/sessions"
	"github.com/stefanoschrs/mealpal/internal/config"
	"github.com/stefanoschrs/mealpal/internal/models"
	"github.com/stefanoschrs/mealpal/internal/services"
	"golang.org/x/oauth2"
)

func TestNewAuthHandler(t *testing.T) {
	cfg := &config.Config{
		GoogleClientID:     "test_client_id",
		GoogleClientSecret: "test_client_secret",
		GoogleRedirectURL:  "http://localhost:8080/auth/google/callback",
		GeminiAPIKey:       "test_gemini_key",
	}

	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	authService := services.NewAuthService(cfg)
	store := sessions.NewCookieStore([]byte("test-secret"))
	
	geminiService := services.NewGeminiService(cfg)
	userStore := services.NewUserStore()
	sheetsService := services.NewSheetsService(authService.GetOAuthConfig(), userStore)
	foodLogService := services.NewFoodLogService(geminiService, sheetsService, userStore)

	handler := NewAuthHandler(authService, store, foodLogService)

	if handler == nil {
		t.Fatal("Expected NewAuthHandler to return a non-nil handler")
	}

	if handler.authService != authService {
		t.Error("Expected authService to be set correctly")
	}

	if handler.store != store {
		t.Error("Expected store to be set correctly")
	}

	if handler.foodLogService != foodLogService {
		t.Error("Expected foodLogService to be set correctly")
	}
}

func TestAuthHandler_Login(t *testing.T) {
	gin.SetMode(gin.TestMode)

	cfg := &config.Config{
		GoogleClientID:     "test_client_id",
		GoogleClientSecret: "test_client_secret",
		GoogleRedirectURL:  "http://localhost:8080/auth/google/callback",
		GeminiAPIKey:       "test_gemini_key",
	}

	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	authService := services.NewAuthService(cfg)
	store := sessions.NewCookieStore([]byte("test-secret"))
	
	geminiService := services.NewGeminiService(cfg)
	userStore := services.NewUserStore()
	sheetsService := services.NewSheetsService(authService.GetOAuthConfig(), userStore)
	foodLogService := services.NewFoodLogService(geminiService, sheetsService, userStore)

	handler := NewAuthHandler(authService, store, foodLogService)

	// Create a test router
	router := gin.New()
	router.GET("/auth/google/login", handler.Login)

	// Create a request
	req := httptest.NewRequest("GET", "/auth/google/login", nil)
	w := httptest.NewRecorder()

	router.ServeHTTP(w, req)

	// Should redirect to Google OAuth
	if w.Code != http.StatusFound {
		t.Errorf("Expected status %d, got %d", http.StatusFound, w.Code)
	}

	location := w.Header().Get("Location")
	if !strings.Contains(location, "accounts.google.com") {
		t.Error("Expected redirect to Google OAuth URL")
	}

	if !strings.Contains(location, "client_id=test_client_id") {
		t.Error("Expected redirect URL to contain client ID")
	}

	// Check that state parameter is present
	if !strings.Contains(location, "state=") {
		t.Error("Expected redirect URL to contain state parameter")
	}
}

func TestAuthHandler_Callback_NoState(t *testing.T) {
	gin.SetMode(gin.TestMode)

	cfg := &config.Config{
		GoogleClientID:     "test_client_id",
		GoogleClientSecret: "test_client_secret",
		GoogleRedirectURL:  "http://localhost:8080/auth/google/callback",
		GeminiAPIKey:       "test_gemini_key",
	}

	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	authService := services.NewAuthService(cfg)
	store := sessions.NewCookieStore([]byte("test-secret"))
	
	geminiService := services.NewGeminiService(cfg)
	userStore := services.NewUserStore()
	sheetsService := services.NewSheetsService(authService.GetOAuthConfig(), userStore)
	foodLogService := services.NewFoodLogService(geminiService, sheetsService, userStore)

	handler := NewAuthHandler(authService, store, foodLogService)

	// Create a test router with HTML templates
	router := gin.New()
	// Set up a simple HTML template for testing
	router.SetHTMLTemplate(template.Must(template.New("").Parse(`
		{{define "error.html"}}
		<html><head><title>{{.title}}</title></head><body>Error: {{.error}}</body></html>
		{{end}}
	`)))
	router.GET("/auth/google/callback", handler.Callback)

	// Create a request without state parameter
	req := httptest.NewRequest("GET", "/auth/google/callback?code=test_code", nil)
	w := httptest.NewRecorder()

	router.ServeHTTP(w, req)

	// Should return error page
	if w.Code != http.StatusBadRequest {
		t.Errorf("Expected status %d, got %d", http.StatusBadRequest, w.Code)
	}
}

func TestAuthHandler_Callback_NoCode(t *testing.T) {
	gin.SetMode(gin.TestMode)

	cfg := &config.Config{
		GoogleClientID:     "test_client_id",
		GoogleClientSecret: "test_client_secret",
		GoogleRedirectURL:  "http://localhost:8080/auth/google/callback",
		GeminiAPIKey:       "test_gemini_key",
	}

	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	authService := services.NewAuthService(cfg)
	store := sessions.NewCookieStore([]byte("test-secret"))
	
	geminiService := services.NewGeminiService(cfg)
	userStore := services.NewUserStore()
	sheetsService := services.NewSheetsService(authService.GetOAuthConfig(), userStore)
	foodLogService := services.NewFoodLogService(geminiService, sheetsService, userStore)

	handler := NewAuthHandler(authService, store, foodLogService)

	// Create a test router with HTML templates
	router := gin.New()
	// Set up a simple HTML template for testing
	router.SetHTMLTemplate(template.Must(template.New("").Parse(`
		{{define "error.html"}}
		<html><head><title>{{.title}}</title></head><body>Error: {{.error}}</body></html>
		{{end}}
	`)))
	router.GET("/auth/google/callback", handler.Callback)

	// Create a request without code parameter
	req := httptest.NewRequest("GET", "/auth/google/callback?state=test_state", nil)
	w := httptest.NewRecorder()

	// Set up session with state
	session, _ := store.Get(req, "mealpal-session")
	session.Values["oauth_state"] = "test_state"
	session.Save(req, w)

	// Create new request with session cookie
	cookies := w.Result().Cookies()
	req2 := httptest.NewRequest("GET", "/auth/google/callback?state=test_state", nil)
	for _, cookie := range cookies {
		req2.AddCookie(cookie)
	}
	w2 := httptest.NewRecorder()

	router.ServeHTTP(w2, req2)

	// Should return error page
	if w2.Code != http.StatusBadRequest {
		t.Errorf("Expected status %d, got %d", http.StatusBadRequest, w2.Code)
	}
}

func TestAuthHandler_Logout(t *testing.T) {
	gin.SetMode(gin.TestMode)

	cfg := &config.Config{
		GoogleClientID:     "test_client_id",
		GoogleClientSecret: "test_client_secret",
		GoogleRedirectURL:  "http://localhost:8080/auth/google/callback",
		GeminiAPIKey:       "test_gemini_key",
	}

	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	authService := services.NewAuthService(cfg)
	store := sessions.NewCookieStore([]byte("test-secret"))
	
	geminiService := services.NewGeminiService(cfg)
	userStore := services.NewUserStore()
	sheetsService := services.NewSheetsService(authService.GetOAuthConfig(), userStore)
	foodLogService := services.NewFoodLogService(geminiService, sheetsService, userStore)

	handler := NewAuthHandler(authService, store, foodLogService)

	// Create a test router
	router := gin.New()
	router.GET("/auth/logout", handler.Logout)

	// Create a request
	req := httptest.NewRequest("GET", "/auth/logout", nil)
	w := httptest.NewRecorder()

	router.ServeHTTP(w, req)

	// Should redirect to home
	if w.Code != http.StatusFound {
		t.Errorf("Expected status %d, got %d", http.StatusFound, w.Code)
	}

	location := w.Header().Get("Location")
	if location != "/" {
		t.Errorf("Expected redirect to '/', got '%s'", location)
	}
}

func TestGenerateStateToken(t *testing.T) {
	token1 := generateStateToken()
	token2 := generateStateToken()

	if token1 == "" {
		t.Error("Expected generateStateToken to return a non-empty token")
	}

	if token2 == "" {
		t.Error("Expected generateStateToken to return a non-empty token")
	}

	if token1 == token2 {
		t.Error("Expected generateStateToken to return different tokens on each call")
	}

	// Token should be base64 encoded
	if len(token1) < 40 { // 32 bytes base64 encoded should be at least 44 characters, but allowing some margin
		t.Errorf("Expected token to be at least 40 characters, got %d", len(token1))
	}
}

func TestAuthHandler_MergeUserData_NewUser(t *testing.T) {
	cfg := &config.Config{
		GoogleClientID:     "test_client_id",
		GoogleClientSecret: "test_client_secret",
		GoogleRedirectURL:  "http://localhost:8080/auth/google/callback",
		GeminiAPIKey:       "test_gemini_key",
	}

	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	authService := services.NewAuthService(cfg)
	store := sessions.NewCookieStore([]byte("test-secret"))
	
	geminiService := services.NewGeminiService(cfg)
	userStore := services.NewUserStore()
	sheetsService := services.NewSheetsService(authService.GetOAuthConfig(), userStore)
	foodLogService := services.NewFoodLogService(geminiService, sheetsService, userStore)

	handler := NewAuthHandler(authService, store, foodLogService)

	// Test with new user (not in store)
	googleUser := &models.User{
		ID:           "new_user_123",
		Email:        "<EMAIL>",
		Name:         "New User",
		Picture:      "https://example.com/pic.jpg",
		AccessToken:  "new_access_token",
		RefreshToken: "new_refresh_token",
	}

	mergedUser := handler.mergeUserData(googleUser)

	// Should return the same user since it's new
	if mergedUser.ID != googleUser.ID {
		t.Errorf("Expected ID %s, got %s", googleUser.ID, mergedUser.ID)
	}

	if mergedUser.Email != googleUser.Email {
		t.Errorf("Expected Email %s, got %s", googleUser.Email, mergedUser.Email)
	}

	if mergedUser.SpreadsheetID != "" {
		t.Errorf("Expected empty SpreadsheetID for new user, got %s", mergedUser.SpreadsheetID)
	}
}

func TestAuthHandler_MergeUserData_ExistingUser(t *testing.T) {
	cfg := &config.Config{
		GoogleClientID:     "test_client_id",
		GoogleClientSecret: "test_client_secret",
		GoogleRedirectURL:  "http://localhost:8080/auth/google/callback",
		GeminiAPIKey:       "test_gemini_key",
	}

	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	authService := services.NewAuthService(cfg)
	store := sessions.NewCookieStore([]byte("test-secret"))

	geminiService := services.NewGeminiService(cfg)
	userStore := services.NewUserStore()
	sheetsService := services.NewSheetsService(authService.GetOAuthConfig(), userStore)
	foodLogService := services.NewFoodLogService(geminiService, sheetsService, userStore)

	handler := NewAuthHandler(authService, store, foodLogService)

	// First, save an existing user
	existingUser := &models.User{
		ID:            "existing_user_123",
		Email:         "<EMAIL>",
		Name:          "Existing User",
		SpreadsheetID: "existing_spreadsheet_123",
	}
	userStore.SaveUser(existingUser)

	// Test with existing user (updated info from Google)
	googleUser := &models.User{
		ID:           "existing_user_123",
		Email:        "<EMAIL>",
		Name:         "Updated Name", // Name updated
		Picture:      "https://example.com/new_pic.jpg", // Picture updated
		AccessToken:  "new_access_token",
		RefreshToken: "new_refresh_token",
	}

	mergedUser := handler.mergeUserData(googleUser)

	// Should preserve existing SpreadsheetID
	if mergedUser.SpreadsheetID != "existing_spreadsheet_123" {
		t.Errorf("Expected SpreadsheetID %s, got %s", "existing_spreadsheet_123", mergedUser.SpreadsheetID)
	}

	// Should update other fields
	if mergedUser.Name != "Updated Name" {
		t.Errorf("Expected Name %s, got %s", "Updated Name", mergedUser.Name)
	}

	if mergedUser.Picture != "https://example.com/new_pic.jpg" {
		t.Errorf("Expected Picture %s, got %s", "https://example.com/new_pic.jpg", mergedUser.Picture)
	}

	if mergedUser.AccessToken != "new_access_token" {
		t.Errorf("Expected AccessToken %s, got %s", "new_access_token", mergedUser.AccessToken)
	}
}

func TestAuthHandler_Callback_InvalidState(t *testing.T) {
	gin.SetMode(gin.TestMode)

	cfg := &config.Config{
		GoogleClientID:     "test_client_id",
		GoogleClientSecret: "test_client_secret",
		GoogleRedirectURL:  "http://localhost:8080/auth/google/callback",
		GeminiAPIKey:       "test_gemini_key",
	}

	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	authService := services.NewAuthService(cfg)
	store := sessions.NewCookieStore([]byte("test-secret"))

	geminiService := services.NewGeminiService(cfg)
	userStore := services.NewUserStore()
	sheetsService := services.NewSheetsService(authService.GetOAuthConfig(), userStore)
	foodLogService := services.NewFoodLogService(geminiService, sheetsService, userStore)

	handler := NewAuthHandler(authService, store, foodLogService)

	// Create a test router with HTML templates
	router := gin.New()
	// Set up a simple HTML template for testing
	router.SetHTMLTemplate(template.Must(template.New("").Parse(`
		{{define "error.html"}}
		<html><head><title>{{.title}}</title></head><body>Error: {{.error}}</body></html>
		{{end}}
	`)))
	router.GET("/auth/google/callback", handler.Callback)

	// Create a request with mismatched state
	req := httptest.NewRequest("GET", "/auth/google/callback?state=wrong_state&code=test_code", nil)
	w := httptest.NewRecorder()

	// Set up session with different state
	session, _ := store.Get(req, "mealpal-session")
	session.Values["oauth_state"] = "correct_state"
	session.Save(req, w)

	// Create new request with session cookie
	cookies := w.Result().Cookies()
	req2 := httptest.NewRequest("GET", "/auth/google/callback?state=wrong_state&code=test_code", nil)
	for _, cookie := range cookies {
		req2.AddCookie(cookie)
	}
	w2 := httptest.NewRecorder()

	router.ServeHTTP(w2, req2)

	// Should return error page
	if w2.Code != http.StatusBadRequest {
		t.Errorf("Expected status %d, got %d", http.StatusBadRequest, w2.Code)
	}
}

func TestAuthHandler_CreateSpreadsheetForUser(t *testing.T) {
	cfg := &config.Config{
		GoogleClientID:     "test_client_id",
		GoogleClientSecret: "test_client_secret",
		GoogleRedirectURL:  "http://localhost:8080/auth/google/callback",
		GeminiAPIKey:       "test_gemini_key",
	}

	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	authService := services.NewAuthService(cfg)
	store := sessions.NewCookieStore([]byte("test-secret"))

	geminiService := services.NewGeminiService(cfg)
	userStore := services.NewUserStore()
	sheetsService := services.NewSheetsService(authService.GetOAuthConfig(), userStore)
	foodLogService := services.NewFoodLogService(geminiService, sheetsService, userStore)

	handler := NewAuthHandler(authService, store, foodLogService)

	user := &models.User{
		ID:    "test123",
		Email: "<EMAIL>",
		Name:  "Test User",
	}

	token := &oauth2.Token{
		AccessToken: "invalid_token",
	}

	ctx := context.Background()

	// This will fail with invalid credentials
	err := handler.createSpreadsheetForUser(ctx, user, token)
	if err == nil {
		t.Error("Expected createSpreadsheetForUser to fail with invalid credentials")
	}

	if !strings.Contains(err.Error(), "failed to create spreadsheet") {
		t.Errorf("Expected error about failed to create spreadsheet, got: %v", err)
	}
}

func TestAuthHandler_Callback_SuccessFlow(t *testing.T) {
	gin.SetMode(gin.TestMode)

	cfg := &config.Config{
		GoogleClientID:     "test_client_id",
		GoogleClientSecret: "test_client_secret",
		GoogleRedirectURL:  "http://localhost:8080/auth/google/callback",
		GeminiAPIKey:       "test_gemini_key",
	}

	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	authService := services.NewAuthService(cfg)
	store := sessions.NewCookieStore([]byte("test-secret"))

	geminiService := services.NewGeminiService(cfg)
	userStore := services.NewUserStore()
	sheetsService := services.NewSheetsService(authService.GetOAuthConfig(), userStore)
	foodLogService := services.NewFoodLogService(geminiService, sheetsService, userStore)

	handler := NewAuthHandler(authService, store, foodLogService)

	// Create a test router with HTML templates
	router := gin.New()
	// Set up a simple HTML template for testing
	router.SetHTMLTemplate(template.Must(template.New("").Parse(`
		{{define "error.html"}}
		<html><head><title>{{.title}}</title></head><body>Error: {{.error}}</body></html>
		{{end}}
	`)))
	router.GET("/auth/google/callback", handler.Callback)

	// Create a request with valid state and code (will fail at OAuth exchange)
	req := httptest.NewRequest("GET", "/auth/google/callback?state=test_state&code=test_code", nil)
	w := httptest.NewRecorder()

	// Set up session with matching state
	session, _ := store.Get(req, "mealpal-session")
	session.Values["oauth_state"] = "test_state"
	session.Save(req, w)

	// Create new request with session cookie
	cookies := w.Result().Cookies()
	req2 := httptest.NewRequest("GET", "/auth/google/callback?state=test_state&code=test_code", nil)
	for _, cookie := range cookies {
		req2.AddCookie(cookie)
	}
	w2 := httptest.NewRecorder()

	router.ServeHTTP(w2, req2)

	// Should return error page due to invalid OAuth credentials
	if w2.Code != http.StatusInternalServerError {
		t.Errorf("Expected status %d, got %d", http.StatusInternalServerError, w2.Code)
	}
}

func TestAuthHandler_Callback_SessionSaveError(t *testing.T) {
	gin.SetMode(gin.TestMode)

	cfg := &config.Config{
		GoogleClientID:     "test_client_id",
		GoogleClientSecret: "test_client_secret",
		GoogleRedirectURL:  "http://localhost:8080/auth/google/callback",
		GeminiAPIKey:       "test_gemini_key",
	}

	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	authService := services.NewAuthService(cfg)
	store := sessions.NewCookieStore([]byte("test-secret"))

	geminiService := services.NewGeminiService(cfg)
	userStore := services.NewUserStore()
	sheetsService := services.NewSheetsService(authService.GetOAuthConfig(), userStore)
	foodLogService := services.NewFoodLogService(geminiService, sheetsService, userStore)

	handler := NewAuthHandler(authService, store, foodLogService)

	// Create a test router with HTML templates
	router := gin.New()
	// Set up a simple HTML template for testing
	router.SetHTMLTemplate(template.Must(template.New("").Parse(`
		{{define "error.html"}}
		<html><head><title>{{.title}}</title></head><body>Error: {{.error}}</body></html>
		{{end}}
	`)))
	router.GET("/auth/google/callback", handler.Callback)

	// Create a request with valid state and code (will fail at OAuth exchange)
	req := httptest.NewRequest("GET", "/auth/google/callback?state=test_state&code=test_code", nil)
	w := httptest.NewRecorder()

	// Set up session with matching state
	session, _ := store.Get(req, "mealpal-session")
	session.Values["oauth_state"] = "test_state"
	session.Save(req, w)

	// Create new request with session cookie
	cookies := w.Result().Cookies()
	req2 := httptest.NewRequest("GET", "/auth/google/callback?state=test_state&code=test_code", nil)
	for _, cookie := range cookies {
		req2.AddCookie(cookie)
	}
	w2 := httptest.NewRecorder()

	router.ServeHTTP(w2, req2)

	// Should return error page due to OAuth exchange failure
	if w2.Code != http.StatusInternalServerError {
		t.Errorf("Expected status %d, got %d", http.StatusInternalServerError, w2.Code)
	}
}

func TestAuthHandler_CreateSpreadsheetForUser_Success(t *testing.T) {
	cfg := &config.Config{
		GoogleClientID:     "test_client_id",
		GoogleClientSecret: "test_client_secret",
		GoogleRedirectURL:  "http://localhost:8080/auth/google/callback",
		GeminiAPIKey:       "test_gemini_key",
	}

	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	authService := services.NewAuthService(cfg)
	store := sessions.NewCookieStore([]byte("test-secret"))

	geminiService := services.NewGeminiService(cfg)
	userStore := services.NewUserStore()
	sheetsService := services.NewSheetsService(authService.GetOAuthConfig(), userStore)
	foodLogService := services.NewFoodLogService(geminiService, sheetsService, userStore)

	handler := NewAuthHandler(authService, store, foodLogService)

	user := &models.User{
		ID:    "test123",
		Email: "<EMAIL>",
		Name:  "Test User",
	}

	token := &oauth2.Token{
		AccessToken: "invalid_token",
	}

	ctx := context.Background()

	// This will fail with invalid credentials, but we test the error path
	err := handler.createSpreadsheetForUser(ctx, user, token)
	if err == nil {
		t.Error("Expected createSpreadsheetForUser to fail with invalid credentials")
	}

	if !strings.Contains(err.Error(), "failed to create initial spreadsheet") {
		t.Errorf("Expected error about failed to create initial spreadsheet, got: %v", err)
	}
}

func TestAuthHandler_Callback_GetUserInfoError(t *testing.T) {
	gin.SetMode(gin.TestMode)

	cfg := &config.Config{
		GoogleClientID:     "test_client_id",
		GoogleClientSecret: "test_client_secret",
		GoogleRedirectURL:  "http://localhost:8080/auth/google/callback",
		GeminiAPIKey:       "test_gemini_key",
	}

	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	authService := services.NewAuthService(cfg)
	store := sessions.NewCookieStore([]byte("test-secret"))

	geminiService := services.NewGeminiService(cfg)
	userStore := services.NewUserStore()
	sheetsService := services.NewSheetsService(authService.GetOAuthConfig(), userStore)
	foodLogService := services.NewFoodLogService(geminiService, sheetsService, userStore)

	handler := NewAuthHandler(authService, store, foodLogService)

	// Create a test router with HTML templates
	router := gin.New()
	// Set up a simple HTML template for testing
	router.SetHTMLTemplate(template.Must(template.New("").Parse(`
		{{define "error.html"}}
		<html><head><title>{{.title}}</title></head><body>Error: {{.error}}</body></html>
		{{end}}
	`)))
	router.GET("/auth/google/callback", handler.Callback)

	// Create a request with valid state and code (will fail at OAuth exchange)
	req := httptest.NewRequest("GET", "/auth/google/callback?state=test_state&code=test_code", nil)
	w := httptest.NewRecorder()

	// Set up session with matching state
	session, _ := store.Get(req, "mealpal-session")
	session.Values["oauth_state"] = "test_state"
	session.Save(req, w)

	// Create new request with session cookie
	cookies := w.Result().Cookies()
	req2 := httptest.NewRequest("GET", "/auth/google/callback?state=test_state&code=test_code", nil)
	for _, cookie := range cookies {
		req2.AddCookie(cookie)
	}
	w2 := httptest.NewRecorder()

	router.ServeHTTP(w2, req2)

	// Should return error page due to OAuth exchange failure
	if w2.Code != http.StatusInternalServerError {
		t.Errorf("Expected status %d, got %d", http.StatusInternalServerError, w2.Code)
	}

	// Check that the error message is about failed to exchange authorization code
	body := w2.Body.String()
	if !strings.Contains(body, "Failed to exchange authorization code") {
		t.Error("Expected error message about failed to exchange authorization code")
	}
}

func TestAuthHandler_Callback_SessionGetError(t *testing.T) {
	gin.SetMode(gin.TestMode)

	cfg := &config.Config{
		GoogleClientID:     "test_client_id",
		GoogleClientSecret: "test_client_secret",
		GoogleRedirectURL:  "http://localhost:8080/auth/google/callback",
		GeminiAPIKey:       "test_gemini_key",
	}

	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	authService := services.NewAuthService(cfg)
	// Use invalid secret to cause session error
	store := sessions.NewCookieStore([]byte("short"))

	geminiService := services.NewGeminiService(cfg)
	userStore := services.NewUserStore()
	sheetsService := services.NewSheetsService(authService.GetOAuthConfig(), userStore)
	foodLogService := services.NewFoodLogService(geminiService, sheetsService, userStore)

	handler := NewAuthHandler(authService, store, foodLogService)

	// Create a test router with HTML templates
	router := gin.New()
	// Set up a simple HTML template for testing
	router.SetHTMLTemplate(template.Must(template.New("").Parse(`
		{{define "error.html"}}
		<html><head><title>{{.title}}</title></head><body>Error: {{.error}}</body></html>
		{{end}}
	`)))
	router.GET("/auth/google/callback", handler.Callback)

	// Create a request
	req := httptest.NewRequest("GET", "/auth/google/callback?state=test_state&code=test_code", nil)
	w := httptest.NewRecorder()

	router.ServeHTTP(w, req)

	// The session error might not always trigger with a short key
	// Let's just check that we get an error response
	if w.Code != http.StatusBadRequest && w.Code != http.StatusInternalServerError {
		t.Errorf("Expected status %d or %d, got %d", http.StatusBadRequest, http.StatusInternalServerError, w.Code)
	}

	// Check that we get some kind of error response
	body := w.Body.String()
	if !strings.Contains(body, "error") && !strings.Contains(body, "Error") {
		t.Error("Expected some kind of error message")
	}
}

func TestAuthHandler_Callback_MockSuccessFlow(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Create mock OAuth and userinfo servers
	oauthServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if strings.Contains(r.URL.Path, "token") {
			// Mock successful token response
			response := map[string]interface{}{
				"access_token":  "mock_access_token",
				"refresh_token": "mock_refresh_token",
				"token_type":    "Bearer",
				"expires_in":    3600,
			}
			w.Header().Set("Content-Type", "application/json")
			json.NewEncoder(w).Encode(response)
		}
	}))
	defer oauthServer.Close()

	userinfoServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if strings.Contains(r.URL.Path, "userinfo") {
			// Mock user info response
			userInfo := map[string]interface{}{
				"id":      "123456789",
				"email":   "<EMAIL>",
				"name":    "Test User",
				"picture": "https://example.com/pic.jpg",
			}
			w.Header().Set("Content-Type", "application/json")
			json.NewEncoder(w).Encode(userInfo)
		}
	}))
	defer userinfoServer.Close()

	cfg := &config.Config{
		GoogleClientID:     "test_client_id",
		GoogleClientSecret: "test_client_secret",
		GoogleRedirectURL:  "http://localhost:8080/auth/google/callback",
		GeminiAPIKey:       "test_gemini_key",
	}

	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	authService := services.NewAuthService(cfg)
	store := sessions.NewCookieStore([]byte("test-secret"))

	geminiService := services.NewGeminiService(cfg)
	userStore := services.NewUserStore()
	sheetsService := services.NewSheetsService(authService.GetOAuthConfig(), userStore)
	foodLogService := services.NewFoodLogService(geminiService, sheetsService, userStore)

	handler := NewAuthHandler(authService, store, foodLogService)

	// Override OAuth endpoints to use our mock servers
	authService.GetOAuthConfig().Endpoint.TokenURL = oauthServer.URL + "/token"

	// Create a test router
	router := gin.New()
	router.GET("/auth/google/callback", handler.Callback)

	// Create a request with valid state and code
	req := httptest.NewRequest("GET", "/auth/google/callback?state=test_state&code=test_code", nil)
	w := httptest.NewRecorder()

	// Set up session with matching state
	session, _ := store.Get(req, "mealpal-session")
	session.Values["oauth_state"] = "test_state"
	session.Save(req, w)

	// Create new request with session cookie
	cookies := w.Result().Cookies()
	req2 := httptest.NewRequest("GET", "/auth/google/callback?state=test_state&code=test_code", nil)
	for _, cookie := range cookies {
		req2.AddCookie(cookie)
	}
	w2 := httptest.NewRecorder()

	router.ServeHTTP(w2, req2)

	// Should redirect to dashboard on success, or fail at some step
	if w2.Code != http.StatusFound && w2.Code != http.StatusInternalServerError {
		t.Errorf("Expected status %d or %d, got %d", http.StatusFound, http.StatusInternalServerError, w2.Code)
	}

	// If it redirects, it should go to dashboard
	if w2.Code == http.StatusFound {
		location := w2.Header().Get("Location")
		if location != "/dashboard" {
			t.Errorf("Expected redirect to '/dashboard', got '%s'", location)
		}
	}
}

package handlers

import (
	"context"
	"crypto/rand"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/sessions"
	"github.com/stefanoschrs/mealpal/internal/middleware"
	"github.com/stefanoschrs/mealpal/internal/models"
	"github.com/stefanoschrs/mealpal/internal/services"
	"golang.org/x/oauth2"
)

type RedisAuthHandler struct {
	authService    *services.AuthService
	store          sessions.Store
	foodLogService *services.FoodLogService
	sessionService *services.SessionService
}

func NewRedisAuthHandler(authService *services.AuthService, store sessions.Store, foodLogService *services.FoodLogService, sessionService *services.SessionService) *RedisAuthHandler {
	return &RedisAuthHandler{
		authService:    authService,
		store:          store,
		foodLogService: foodLogService,
		sessionService: sessionService,
	}
}

func (h *RedisAuthHandler) Login(c *gin.Context) {
	// Generate a random state token
	state := generateRedisStateToken()
	
	// Store state in session for verification
	session, _ := h.store.Get(c.Request, middleware.SessionName)
	session.Values["oauth_state"] = state
	session.Save(c.Request, c.Writer)

	// Redirect to Google OAuth
	authURL := h.authService.GetAuthURL(state)
	c.Redirect(http.StatusFound, authURL)
}

func (h *RedisAuthHandler) Callback(c *gin.Context) {
	// Verify state token
	session, err := h.store.Get(c.Request, middleware.SessionName)
	if err != nil {
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"error": "Session error",
		})
		return
	}

	storedState, ok := session.Values["oauth_state"].(string)
	if !ok || storedState != c.Query("state") {
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"error": "Invalid state token",
		})
		return
	}

	// Exchange code for token
	code := c.Query("code")
	if code == "" {
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"error": "No authorization code received",
		})
		return
	}

	var token *oauth2.Token
	token, err = h.authService.ExchangeCode(c.Request.Context(), code)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": "Failed to exchange authorization code",
		})
		return
	}

	// Get user info from Google
	user, err := h.authService.GetUserInfo(c.Request.Context(), token)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": "Failed to get user information",
		})
		return
	}

	// Check if this is an existing user and merge stored data
	finalUser := h.mergeUserData(user)

	// For new users, create spreadsheet immediately
	if finalUser.SpreadsheetID == "" {
		err = h.createSpreadsheetForUser(c.Request.Context(), finalUser, token)
		if err != nil {
			// Log error but don't fail login - spreadsheet can be created later
			fmt.Printf("Warning: Failed to create spreadsheet for new user %s: %v\n", finalUser.Email, err)
		}
	}

	// Create Redis session
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	sessionID, err := h.sessionService.CreateSession(ctx, finalUser.ID, token)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": "Failed to create session",
		})
		return
	}

	// Store minimal session data in cookie
	sessionData := models.SessionData{
		SessionID:  sessionID,
		IsLoggedIn: true,
	}

	sessionDataJSON, err := json.Marshal(sessionData)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": "Failed to save session",
		})
		return
	}

	session.Values["session_data"] = string(sessionDataJSON)
	session.Save(c.Request, c.Writer)

	// Redirect to dashboard
	c.Redirect(http.StatusFound, "/dashboard")
}

func (h *RedisAuthHandler) Logout(c *gin.Context) {
	session, _ := h.store.Get(c.Request, middleware.SessionName)
	
	// Get session data to clean up Redis session
	sessionDataJSON, ok := session.Values["session_data"].(string)
	if ok && sessionDataJSON != "" {
		var sessionData models.SessionData
		if err := json.Unmarshal([]byte(sessionDataJSON), &sessionData); err == nil {
			// Delete Redis session
			ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
			defer cancel()
			h.sessionService.DeleteSession(ctx, sessionData.SessionID)
		}
	}
	
	// Clear cookie session
	session.Values = make(map[interface{}]interface{})
	session.Options.MaxAge = -1
	session.Save(c.Request, c.Writer)

	c.Redirect(http.StatusFound, "/")
}

// mergeUserData merges fresh Google user data with existing stored user data
func (h *RedisAuthHandler) mergeUserData(googleUser *models.User) *models.User {
	// Check if user exists in store
	storedUser, exists := h.foodLogService.GetUserStore().GetUser(googleUser.ID)
	if exists {
		// Merge: use stored data but update with fresh Google info
		storedUser.Email = googleUser.Email
		storedUser.Name = googleUser.Name
		storedUser.Picture = googleUser.Picture
		// Don't store tokens in user store anymore - they go to Redis
		// Keep existing SpreadsheetID

		// Save updated user data (without tokens)
		h.foodLogService.GetUserStore().SaveUser(storedUser)
		fmt.Printf("DEBUG: Existing user %s logged in, SpreadsheetID: %s\n", storedUser.Email, storedUser.SpreadsheetID)
		return storedUser
	}

	// New user - save to store (without tokens)
	googleUser.AccessToken = ""
	googleUser.RefreshToken = ""
	h.foodLogService.GetUserStore().SaveUser(googleUser)
	fmt.Printf("DEBUG: New user %s signed up\n", googleUser.Email)
	return googleUser
}

// createSpreadsheetForUser creates a spreadsheet for a new user
func (h *RedisAuthHandler) createSpreadsheetForUser(ctx context.Context, user *models.User, token *oauth2.Token) error {
	// Use the sheets service to create an empty spreadsheet
	sheetsService := services.NewSheetsService(h.authService.GetOAuthConfig(), h.foodLogService.GetUserStore())

	err := sheetsService.CreateSpreadsheetForUser(ctx, user, token)
	if err != nil {
		return fmt.Errorf("failed to create initial spreadsheet: %w", err)
	}

	fmt.Printf("DEBUG: Created spreadsheet for new user %s\n", user.Email)
	return nil
}

func generateRedisStateToken() string {
	b := make([]byte, 32)
	rand.Read(b)
	return base64.URLEncoding.EncodeToString(b)
}

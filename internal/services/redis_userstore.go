package services

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/stefanoschrs/mealpal/internal/models"
)

// RedisUserStore provides Redis-based storage for user data
type RedisUserStore struct {
	redisService *RedisService
}

// NewRedisUserStore creates a new Redis-based user store
func NewRedisUserStore(redisService *RedisService) *RedisUserStore {
	return &RedisUserStore{
		redisService: redisService,
	}
}

// GetUser retrieves a user by ID from Redis
func (s *RedisUserStore) GetUser(userID string) (*models.User, bool) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	key := fmt.Sprintf("user:%s", userID)
	data, err := s.redisService.Get(ctx, key)
	if err != nil {
		return nil, false
	}

	var user models.User
	err = json.Unmarshal([]byte(data), &user)
	if err != nil {
		fmt.Printf("Warning: Failed to unmarshal user data for %s: %v\n", userID, err)
		return nil, false
	}

	return &user, true
}

// SaveUser saves a user to Redis
func (s *RedisUserStore) SaveUser(user *models.User) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// Clear tokens before saving (they're stored in sessions)
	userCopy := *user
	userCopy.AccessToken = ""
	userCopy.RefreshToken = ""

	data, err := json.Marshal(userCopy)
	if err != nil {
		fmt.Printf("Warning: Failed to marshal user data for %s: %v\n", user.ID, err)
		return
	}

	key := fmt.Sprintf("user:%s", user.ID)
	// Store user data with no expiration (persistent)
	err = s.redisService.Set(ctx, key, data, 0)
	if err != nil {
		fmt.Printf("Warning: Failed to save user data for %s: %v\n", user.ID, err)
		return
	}

	// Also maintain a set of all user IDs for GetAllUsers
	userSetKey := "users:all"
	err = s.redisService.GetClient().SAdd(ctx, userSetKey, user.ID).Err()
	if err != nil {
		fmt.Printf("Warning: Failed to add user to users set: %v\n", err)
	}
}

// UpdateSpreadsheetID updates the spreadsheet ID for a user
func (s *RedisUserStore) UpdateSpreadsheetID(userID, spreadsheetID string) {
	user, exists := s.GetUser(userID)
	if !exists {
		fmt.Printf("Warning: User %s not found when updating spreadsheet ID\n", userID)
		return
	}

	user.SpreadsheetID = spreadsheetID
	s.SaveUser(user)
}

// GetAllUsers returns all users from Redis
func (s *RedisUserStore) GetAllUsers() []*models.User {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Get all user IDs from the set
	userSetKey := "users:all"
	userIDs, err := s.redisService.GetClient().SMembers(ctx, userSetKey).Result()
	if err != nil {
		fmt.Printf("Warning: Failed to get user IDs: %v\n", err)
		return []*models.User{}
	}

	users := make([]*models.User, 0, len(userIDs))
	for _, userID := range userIDs {
		user, exists := s.GetUser(userID)
		if exists {
			users = append(users, user)
		}
	}

	return users
}

// MigrateFromFileStore migrates user data from the old file-based store to Redis
func (s *RedisUserStore) MigrateFromFileStore(fileStore *UserStore) error {
	fmt.Println("🔄 Migrating user data from file store to Redis...")
	
	allUsers := fileStore.GetAllUsers()
	if len(allUsers) == 0 {
		fmt.Println("✅ No users to migrate")
		return nil
	}

	fmt.Printf("📦 Migrating %d users to Redis...\n", len(allUsers))
	
	for _, user := range allUsers {
		s.SaveUser(user)
		fmt.Printf("   ✅ Migrated user: %s (SpreadsheetID: %s)\n", user.Email, user.SpreadsheetID)
	}

	fmt.Printf("✅ Successfully migrated %d users to Redis\n", len(allUsers))
	return nil
}

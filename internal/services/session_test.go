package services

import (
	"context"
	"testing"
	"time"

	"github.com/stefanoschrs/mealpal/internal/config"
	"golang.org/x/oauth2"
)

func setupSessionService(t *testing.T) (*SessionService, func()) {
	cfg := &config.Config{
		RedisHost:     "localhost",
		RedisPort:     "12522",
		RedisPassword: "hello-redis",
		RedisDB:       0,
	}

	redisService, err := NewRedisService(cfg)
	if err != nil {
		t.Skipf("Redis not available, skipping test: %v", err)
	}

	sessionService := NewSessionService(redisService)

	cleanup := func() {
		redisService.Close()
	}

	return sessionService, cleanup
}

func TestNewSessionService(t *testing.T) {
	sessionService, cleanup := setupSessionService(t)
	defer cleanup()

	if sessionService == nil {
		t.Fatal("Expected NewSessionService to return a non-nil service")
	}

	if sessionService.redisService == nil {
		t.Fatal("Expected Redis service to be set")
	}
}

func TestSessionService_CreateSession(t *testing.T) {
	sessionService, cleanup := setupSessionService(t)
	defer cleanup()

	ctx := context.Background()
	userID := "test_user_123"
	token := &oauth2.Token{
		AccessToken:  "access_token_123",
		RefreshToken: "refresh_token_123",
		Expiry:       time.Now().Add(time.Hour),
	}

	sessionID, err := sessionService.CreateSession(ctx, userID, token)
	if err != nil {
		t.Fatalf("Failed to create session: %v", err)
	}

	if sessionID == "" {
		t.Error("Expected non-empty session ID")
	}

	// Clean up
	sessionService.DeleteSession(ctx, sessionID)
}

func TestSessionService_GetSession(t *testing.T) {
	sessionService, cleanup := setupSessionService(t)
	defer cleanup()

	ctx := context.Background()
	userID := "test_user_456"
	token := &oauth2.Token{
		AccessToken:  "access_token_456",
		RefreshToken: "refresh_token_456",
		Expiry:       time.Now().Add(time.Hour),
	}

	// Create session
	sessionID, err := sessionService.CreateSession(ctx, userID, token)
	if err != nil {
		t.Fatalf("Failed to create session: %v", err)
	}

	// Get session
	sessionData, err := sessionService.GetSession(ctx, sessionID)
	if err != nil {
		t.Fatalf("Failed to get session: %v", err)
	}

	if sessionData.UserID != userID {
		t.Errorf("Expected UserID %s, got %s", userID, sessionData.UserID)
	}

	if sessionData.AccessToken != token.AccessToken {
		t.Errorf("Expected AccessToken %s, got %s", token.AccessToken, sessionData.AccessToken)
	}

	if sessionData.RefreshToken != token.RefreshToken {
		t.Errorf("Expected RefreshToken %s, got %s", token.RefreshToken, sessionData.RefreshToken)
	}

	// Clean up
	sessionService.DeleteSession(ctx, sessionID)
}

func TestSessionService_UpdateSessionTokens(t *testing.T) {
	sessionService, cleanup := setupSessionService(t)
	defer cleanup()

	ctx := context.Background()
	userID := "test_user_789"
	originalToken := &oauth2.Token{
		AccessToken:  "original_access_token",
		RefreshToken: "original_refresh_token",
		Expiry:       time.Now().Add(time.Hour),
	}

	// Create session
	sessionID, err := sessionService.CreateSession(ctx, userID, originalToken)
	if err != nil {
		t.Fatalf("Failed to create session: %v", err)
	}

	// Update tokens
	newToken := &oauth2.Token{
		AccessToken:  "new_access_token",
		RefreshToken: "new_refresh_token",
		Expiry:       time.Now().Add(2 * time.Hour),
	}

	err = sessionService.UpdateSessionTokens(ctx, sessionID, newToken)
	if err != nil {
		t.Fatalf("Failed to update session tokens: %v", err)
	}

	// Get updated session
	sessionData, err := sessionService.GetSession(ctx, sessionID)
	if err != nil {
		t.Fatalf("Failed to get updated session: %v", err)
	}

	if sessionData.AccessToken != newToken.AccessToken {
		t.Errorf("Expected updated AccessToken %s, got %s", newToken.AccessToken, sessionData.AccessToken)
	}

	if sessionData.RefreshToken != newToken.RefreshToken {
		t.Errorf("Expected updated RefreshToken %s, got %s", newToken.RefreshToken, sessionData.RefreshToken)
	}

	// Clean up
	sessionService.DeleteSession(ctx, sessionID)
}

func TestSessionService_DeleteSession(t *testing.T) {
	sessionService, cleanup := setupSessionService(t)
	defer cleanup()

	ctx := context.Background()
	userID := "test_user_delete"
	token := &oauth2.Token{
		AccessToken:  "access_token_delete",
		RefreshToken: "refresh_token_delete",
		Expiry:       time.Now().Add(time.Hour),
	}

	// Create session
	sessionID, err := sessionService.CreateSession(ctx, userID, token)
	if err != nil {
		t.Fatalf("Failed to create session: %v", err)
	}

	// Delete session
	err = sessionService.DeleteSession(ctx, sessionID)
	if err != nil {
		t.Fatalf("Failed to delete session: %v", err)
	}

	// Try to get deleted session
	_, err = sessionService.GetSession(ctx, sessionID)
	if err == nil {
		t.Error("Expected error when getting deleted session, but got none")
	}
}

func TestSessionService_GetUserSessions(t *testing.T) {
	sessionService, cleanup := setupSessionService(t)
	defer cleanup()

	ctx := context.Background()
	userID := "test_user_multiple"
	token := &oauth2.Token{
		AccessToken:  "access_token_multiple",
		RefreshToken: "refresh_token_multiple",
		Expiry:       time.Now().Add(time.Hour),
	}

	// Create multiple sessions for the same user
	sessionID1, err := sessionService.CreateSession(ctx, userID, token)
	if err != nil {
		t.Fatalf("Failed to create first session: %v", err)
	}

	sessionID2, err := sessionService.CreateSession(ctx, userID, token)
	if err != nil {
		t.Fatalf("Failed to create second session: %v", err)
	}

	// Get user sessions
	sessionIDs, err := sessionService.GetUserSessions(ctx, userID)
	if err != nil {
		t.Fatalf("Failed to get user sessions: %v", err)
	}

	if len(sessionIDs) < 2 {
		t.Errorf("Expected at least 2 sessions, got %d", len(sessionIDs))
	}

	// Check that both session IDs are in the list
	found1, found2 := false, false
	for _, id := range sessionIDs {
		if id == sessionID1 {
			found1 = true
		}
		if id == sessionID2 {
			found2 = true
		}
	}

	if !found1 {
		t.Error("First session ID not found in user sessions")
	}
	if !found2 {
		t.Error("Second session ID not found in user sessions")
	}

	// Clean up
	sessionService.DeleteSession(ctx, sessionID1)
	sessionService.DeleteSession(ctx, sessionID2)
}

func TestSessionService_GetValidTokenForUser(t *testing.T) {
	sessionService, cleanup := setupSessionService(t)
	defer cleanup()

	ctx := context.Background()
	userID := "test_user_token"
	token := &oauth2.Token{
		AccessToken:  "access_token_valid",
		RefreshToken: "refresh_token_valid",
		Expiry:       time.Now().Add(time.Hour),
	}

	// Create session
	sessionID, err := sessionService.CreateSession(ctx, userID, token)
	if err != nil {
		t.Fatalf("Failed to create session: %v", err)
	}

	// Get valid token for user
	validToken, err := sessionService.GetValidTokenForUser(ctx, userID)
	if err != nil {
		t.Fatalf("Failed to get valid token for user: %v", err)
	}

	if validToken.AccessToken != token.AccessToken {
		t.Errorf("Expected AccessToken %s, got %s", token.AccessToken, validToken.AccessToken)
	}

	if validToken.RefreshToken != token.RefreshToken {
		t.Errorf("Expected RefreshToken %s, got %s", token.RefreshToken, validToken.RefreshToken)
	}

	// Clean up
	sessionService.DeleteSession(ctx, sessionID)
}

func TestSessionService_GetValidTokenForUser_NoSessions(t *testing.T) {
	sessionService, cleanup := setupSessionService(t)
	defer cleanup()

	ctx := context.Background()
	userID := "test_user_no_sessions"

	// Try to get valid token for user with no sessions
	_, err := sessionService.GetValidTokenForUser(ctx, userID)
	if err == nil {
		t.Error("Expected error when getting token for user with no sessions, but got none")
	}
}

package services

import (
	"context"
	"os"
	"testing"

	"github.com/stefanoschrs/mealpal/internal/config"
	"github.com/stefanoschrs/mealpal/internal/models"
)

func TestRedisUserStore_SaveAndGetUser(t *testing.T) {
	cfg := &config.Config{
		RedisHost:     "localhost",
		RedisPort:     "12522",
		RedisPassword: "hello-redis",
		RedisDB:       0,
	}

	redisService, err := NewRedisService(cfg)
	if err != nil {
		t.Skipf("Redis not available, skipping test: %v", err)
	}
	defer redisService.Close()

	store := NewRedisUserStore(redisService)

	user := &models.User{
		ID:            "test123",
		Email:         "<EMAIL>",
		Name:          "Test User",
		Picture:       "https://example.com/pic.jpg",
		AccessToken:   "access_token",
		RefreshToken:  "refresh_token",
		SpreadsheetID: "sheet123",
	}

	// Test saving user
	store.SaveUser(user)

	// Test getting user
	retrievedUser, exists := store.GetUser("test123")
	if !exists {
		t.Fatal("Expected user to exist after saving")
	}

	if retrievedUser.ID != user.ID {
		t.Errorf("Expected ID %s, got %s", user.ID, retrievedUser.ID)
	}
	if retrievedUser.Email != user.Email {
		t.Errorf("Expected email %s, got %s", user.Email, retrievedUser.Email)
	}
	if retrievedUser.SpreadsheetID != user.SpreadsheetID {
		t.Errorf("Expected spreadsheet ID %s, got %s", user.SpreadsheetID, retrievedUser.SpreadsheetID)
	}

	// Tokens should be cleared
	if retrievedUser.AccessToken != "" {
		t.Error("Expected access token to be cleared")
	}
	if retrievedUser.RefreshToken != "" {
		t.Error("Expected refresh token to be cleared")
	}

	// Clean up
	ctx := context.Background()
	redisService.Delete(ctx, "user:test123")
	redisService.GetClient().SRem(ctx, "users:all", "test123")
}

func TestRedisUserStore_UpdateSpreadsheetID(t *testing.T) {
	cfg := &config.Config{
		RedisHost:     "localhost",
		RedisPort:     "12522",
		RedisPassword: "hello-redis",
		RedisDB:       0,
	}

	redisService, err := NewRedisService(cfg)
	if err != nil {
		t.Skipf("Redis not available, skipping test: %v", err)
	}
	defer redisService.Close()

	store := NewRedisUserStore(redisService)

	user := &models.User{
		ID:    "test456",
		Email: "<EMAIL>",
		Name:  "Test User 2",
	}

	// Save user first
	store.SaveUser(user)

	// Update spreadsheet ID
	newSpreadsheetID := "new_sheet_id"
	store.UpdateSpreadsheetID("test456", newSpreadsheetID)

	// Verify update
	retrievedUser, exists := store.GetUser("test456")
	if !exists {
		t.Fatal("Expected user to exist after updating")
	}

	if retrievedUser.SpreadsheetID != newSpreadsheetID {
		t.Errorf("Expected spreadsheet ID %s, got %s", newSpreadsheetID, retrievedUser.SpreadsheetID)
	}

	// Clean up
	ctx := context.Background()
	redisService.Delete(ctx, "user:test456")
	redisService.GetClient().SRem(ctx, "users:all", "test456")
}

func TestRedisUserStore_GetAllUsers(t *testing.T) {
	cfg := &config.Config{
		RedisHost:     "localhost",
		RedisPort:     "12522",
		RedisPassword: "hello-redis",
		RedisDB:       0,
	}

	redisService, err := NewRedisService(cfg)
	if err != nil {
		t.Skipf("Redis not available, skipping test: %v", err)
	}
	defer redisService.Close()

	store := NewRedisUserStore(redisService)

	// Clean up any existing test data
	ctx := context.Background()
	redisService.Delete(ctx, "user:test789")
	redisService.Delete(ctx, "user:test790")
	redisService.GetClient().SRem(ctx, "users:all", "test789", "test790")

	users := []*models.User{
		{
			ID:    "test789",
			Email: "<EMAIL>",
			Name:  "Test User 3",
		},
		{
			ID:    "test790",
			Email: "<EMAIL>",
			Name:  "Test User 4",
		},
	}

	// Save users
	for _, user := range users {
		store.SaveUser(user)
	}

	// Get all users
	allUsers := store.GetAllUsers()

	// Should have at least our test users (might have others from other tests)
	if len(allUsers) < 2 {
		t.Errorf("Expected at least 2 users, got %d", len(allUsers))
	}

	// Check that our test users are in the result
	foundUsers := make(map[string]bool)
	for _, user := range allUsers {
		foundUsers[user.ID] = true
	}

	for _, expectedUser := range users {
		if !foundUsers[expectedUser.ID] {
			t.Errorf("Expected to find user %s in results", expectedUser.ID)
		}
	}

	// Clean up
	for _, user := range users {
		redisService.Delete(ctx, "user:"+user.ID)
		redisService.GetClient().SRem(ctx, "users:all", user.ID)
	}
}

func TestRedisUserStore_GetNonExistentUser(t *testing.T) {
	cfg := &config.Config{
		RedisHost:     "localhost",
		RedisPort:     "12522",
		RedisPassword: "hello-redis",
		RedisDB:       0,
	}

	redisService, err := NewRedisService(cfg)
	if err != nil {
		t.Skipf("Redis not available, skipping test: %v", err)
	}
	defer redisService.Close()

	store := NewRedisUserStore(redisService)

	// Try to get a user that doesn't exist
	user, exists := store.GetUser("nonexistent")

	if exists {
		t.Error("Expected user to not exist")
	}
	if user != nil {
		t.Error("Expected user to be nil when not found")
	}
}

func TestRedisUserStore_MigrateFromFileStore(t *testing.T) {
	cfg := &config.Config{
		RedisHost:     "localhost",
		RedisPort:     "12522",
		RedisPassword: "hello-redis",
		RedisDB:       0,
	}

	redisService, err := NewRedisService(cfg)
	if err != nil {
		t.Skipf("Redis not available, skipping test: %v", err)
	}
	defer redisService.Close()

	// Create a temporary directory for file store testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	// Create file store with test data
	fileStore := NewUserStore()
	testUser := &models.User{
		ID:            "migrate_test",
		Email:         "<EMAIL>",
		Name:          "Migration Test User",
		SpreadsheetID: "migrate_sheet_id",
	}
	fileStore.SaveUser(testUser)

	// Create Redis store and migrate
	redisStore := NewRedisUserStore(redisService)
	err = redisStore.MigrateFromFileStore(fileStore)
	if err != nil {
		t.Fatalf("Migration failed: %v", err)
	}

	// Verify migration
	migratedUser, exists := redisStore.GetUser("migrate_test")
	if !exists {
		t.Fatal("Expected migrated user to exist in Redis")
	}

	if migratedUser.Email != testUser.Email {
		t.Errorf("Expected email %s, got %s", testUser.Email, migratedUser.Email)
	}
	if migratedUser.SpreadsheetID != testUser.SpreadsheetID {
		t.Errorf("Expected spreadsheet ID %s, got %s", testUser.SpreadsheetID, migratedUser.SpreadsheetID)
	}

	// Clean up
	ctx := context.Background()
	redisService.Delete(ctx, "user:migrate_test")
	redisService.GetClient().SRem(ctx, "users:all", "migrate_test")
}

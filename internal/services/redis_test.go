package services

import (
	"context"
	"testing"
	"time"

	"github.com/stefanoschrs/mealpal/internal/config"
)

func TestNewRedisService(t *testing.T) {
	// Skip if Redis is not available
	cfg := &config.Config{
		RedisHost:     "localhost",
		RedisPort:     "12522",
		RedisPassword: "hello-redis",
		RedisDB:       0,
	}

	redisService, err := NewRedisService(cfg)
	if err != nil {
		t.Skipf("Redis not available, skipping test: %v", err)
	}
	defer redisService.Close()

	if redisService == nil {
		t.Fatal("Expected NewRedisService to return a non-nil service")
	}

	if redisService.client == nil {
		t.Fatal("Expected Redis client to be initialized")
	}
}

func TestRedisService_SetAndGet(t *testing.T) {
	cfg := &config.Config{
		RedisHost:     "localhost",
		RedisPort:     "12522",
		RedisPassword: "hello-redis",
		RedisDB:       0,
	}

	redisService, err := NewRedisService(cfg)
	if err != nil {
		t.Skipf("Redis not available, skipping test: %v", err)
	}
	defer redisService.Close()

	ctx := context.Background()
	key := "test_key"
	value := "test_value"

	// Test Set
	err = redisService.Set(ctx, key, value, time.Minute)
	if err != nil {
		t.Fatalf("Failed to set value: %v", err)
	}

	// Test Get
	retrievedValue, err := redisService.Get(ctx, key)
	if err != nil {
		t.Fatalf("Failed to get value: %v", err)
	}

	if retrievedValue != value {
		t.Errorf("Expected value %s, got %s", value, retrievedValue)
	}

	// Clean up
	redisService.Delete(ctx, key)
}

func TestRedisService_Delete(t *testing.T) {
	cfg := &config.Config{
		RedisHost:     "localhost",
		RedisPort:     "12522",
		RedisPassword: "hello-redis",
		RedisDB:       0,
	}

	redisService, err := NewRedisService(cfg)
	if err != nil {
		t.Skipf("Redis not available, skipping test: %v", err)
	}
	defer redisService.Close()

	ctx := context.Background()
	key := "test_delete_key"
	value := "test_value"

	// Set a value
	err = redisService.Set(ctx, key, value, time.Minute)
	if err != nil {
		t.Fatalf("Failed to set value: %v", err)
	}

	// Delete the value
	err = redisService.Delete(ctx, key)
	if err != nil {
		t.Fatalf("Failed to delete value: %v", err)
	}

	// Try to get the deleted value
	_, err = redisService.Get(ctx, key)
	if err == nil {
		t.Error("Expected error when getting deleted key, but got none")
	}
}

func TestRedisService_Exists(t *testing.T) {
	cfg := &config.Config{
		RedisHost:     "localhost",
		RedisPort:     "12522",
		RedisPassword: "hello-redis",
		RedisDB:       0,
	}

	redisService, err := NewRedisService(cfg)
	if err != nil {
		t.Skipf("Redis not available, skipping test: %v", err)
	}
	defer redisService.Close()

	ctx := context.Background()
	key := "test_exists_key"
	value := "test_value"

	// Check non-existent key
	exists, err := redisService.Exists(ctx, key)
	if err != nil {
		t.Fatalf("Failed to check existence: %v", err)
	}
	if exists {
		t.Error("Expected key to not exist")
	}

	// Set a value
	err = redisService.Set(ctx, key, value, time.Minute)
	if err != nil {
		t.Fatalf("Failed to set value: %v", err)
	}

	// Check existing key
	exists, err = redisService.Exists(ctx, key)
	if err != nil {
		t.Fatalf("Failed to check existence: %v", err)
	}
	if !exists {
		t.Error("Expected key to exist")
	}

	// Clean up
	redisService.Delete(ctx, key)
}

func TestRedisService_SetExpiration(t *testing.T) {
	cfg := &config.Config{
		RedisHost:     "localhost",
		RedisPort:     "12522",
		RedisPassword: "hello-redis",
		RedisDB:       0,
	}

	redisService, err := NewRedisService(cfg)
	if err != nil {
		t.Skipf("Redis not available, skipping test: %v", err)
	}
	defer redisService.Close()

	ctx := context.Background()
	key := "test_expiration_key"
	value := "test_value"

	// Set a value without expiration
	err = redisService.Set(ctx, key, value, 0)
	if err != nil {
		t.Fatalf("Failed to set value: %v", err)
	}

	// Set expiration
	err = redisService.SetExpiration(ctx, key, time.Second)
	if err != nil {
		t.Fatalf("Failed to set expiration: %v", err)
	}

	// Wait for expiration
	time.Sleep(2 * time.Second)

	// Check if key expired
	exists, err := redisService.Exists(ctx, key)
	if err != nil {
		t.Fatalf("Failed to check existence: %v", err)
	}
	if exists {
		t.Error("Expected key to have expired")
	}
}

func TestRedisService_GetKeys(t *testing.T) {
	cfg := &config.Config{
		RedisHost:     "localhost",
		RedisPort:     "12522",
		RedisPassword: "hello-redis",
		RedisDB:       0,
	}

	redisService, err := NewRedisService(cfg)
	if err != nil {
		t.Skipf("Redis not available, skipping test: %v", err)
	}
	defer redisService.Close()

	ctx := context.Background()
	pattern := "test_pattern_*"

	// Set some test keys
	keys := []string{"test_pattern_1", "test_pattern_2", "test_pattern_3"}
	for _, key := range keys {
		err = redisService.Set(ctx, key, "value", time.Minute)
		if err != nil {
			t.Fatalf("Failed to set key %s: %v", key, err)
		}
	}

	// Get keys matching pattern
	matchedKeys, err := redisService.GetKeys(ctx, pattern)
	if err != nil {
		t.Fatalf("Failed to get keys: %v", err)
	}

	if len(matchedKeys) < len(keys) {
		t.Errorf("Expected at least %d keys, got %d", len(keys), len(matchedKeys))
	}

	// Clean up
	for _, key := range keys {
		redisService.Delete(ctx, key)
	}
}

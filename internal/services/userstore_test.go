package services

import (
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"testing"
	"time"

	"github.com/stefanoschrs/mealpal/internal/models"
)

func TestNewUserStore(t *testing.T) {
	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	store := NewUserStore()

	if store == nil {
		t.Fatal("Expected NewUserStore to return a non-nil store")
	}

	if store.users == nil {
		t.Fatal("Expected users map to be initialized")
	}

	expectedPath := filepath.Join("data", "users.json")
	if store.filePath != expectedPath {
		t.Errorf("Expected filePath to be %s, got %s", expectedPath, store.filePath)
	}

	// Check that data directory was created
	if _, err := os.Stat("data"); os.IsNotExist(err) {
		t.Error("Expected data directory to be created")
	}
}

func TestUserStore_SaveAndGetUser(t *testing.T) {
	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	store := NewUserStore()

	user := &models.User{
		ID:            "test123",
		Email:         "<EMAIL>",
		Name:          "Test User",
		Picture:       "https://example.com/pic.jpg",
		AccessToken:   "access_token",
		RefreshToken:  "refresh_token",
		SpreadsheetID: "sheet123",
	}

	// Test saving user
	store.SaveUser(user)

	// Test getting user
	retrievedUser, exists := store.GetUser("test123")
	if !exists {
		t.Fatal("Expected user to exist after saving")
	}

	if retrievedUser.ID != user.ID {
		t.Errorf("Expected ID %s, got %s", user.ID, retrievedUser.ID)
	}
	if retrievedUser.Email != user.Email {
		t.Errorf("Expected Email %s, got %s", user.Email, retrievedUser.Email)
	}
	if retrievedUser.Name != user.Name {
		t.Errorf("Expected Name %s, got %s", user.Name, retrievedUser.Name)
	}
	if retrievedUser.SpreadsheetID != user.SpreadsheetID {
		t.Errorf("Expected SpreadsheetID %s, got %s", user.SpreadsheetID, retrievedUser.SpreadsheetID)
	}

	// Test that we get a copy (not the same reference)
	if retrievedUser == user {
		t.Error("Expected GetUser to return a copy, not the same reference")
	}

	// Test getting non-existent user
	_, exists = store.GetUser("nonexistent")
	if exists {
		t.Error("Expected non-existent user to not exist")
	}
}

func TestUserStore_UpdateSpreadsheetID(t *testing.T) {
	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	store := NewUserStore()

	user := &models.User{
		ID:            "test123",
		Email:         "<EMAIL>",
		Name:          "Test User",
		SpreadsheetID: "",
	}

	// Save user without spreadsheet ID
	store.SaveUser(user)

	// Update spreadsheet ID
	newSpreadsheetID := "new_sheet_123"
	store.UpdateSpreadsheetID("test123", newSpreadsheetID)

	// Verify update
	retrievedUser, exists := store.GetUser("test123")
	if !exists {
		t.Fatal("Expected user to exist")
	}

	if retrievedUser.SpreadsheetID != newSpreadsheetID {
		t.Errorf("Expected SpreadsheetID %s, got %s", newSpreadsheetID, retrievedUser.SpreadsheetID)
	}

	// Test updating non-existent user (should not panic)
	store.UpdateSpreadsheetID("nonexistent", "some_id")
}

func TestUserStore_FilePersistence(t *testing.T) {
	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	// Create first store and save a user
	store1 := NewUserStore()
	user := &models.User{
		ID:            "persist123",
		Email:         "<EMAIL>",
		Name:          "Persist User",
		SpreadsheetID: "persist_sheet",
	}
	store1.SaveUser(user)

	// Create second store (should load from file)
	store2 := NewUserStore()

	// Verify user was loaded
	retrievedUser, exists := store2.GetUser("persist123")
	if !exists {
		t.Fatal("Expected user to be loaded from file")
	}

	if retrievedUser.Email != user.Email {
		t.Errorf("Expected Email %s, got %s", user.Email, retrievedUser.Email)
	}
	if retrievedUser.SpreadsheetID != user.SpreadsheetID {
		t.Errorf("Expected SpreadsheetID %s, got %s", user.SpreadsheetID, retrievedUser.SpreadsheetID)
	}
}

func TestUserStore_LoadFromFileErrors(t *testing.T) {
	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	// Create data directory
	os.MkdirAll("data", 0755)

	// Test with invalid JSON file
	invalidJSON := `{"invalid": json}`
	err := os.WriteFile(filepath.Join("data", "users.json"), []byte(invalidJSON), 0644)
	if err != nil {
		t.Fatalf("Failed to write invalid JSON file: %v", err)
	}

	// This should not panic, just log a warning
	store := NewUserStore()
	if store == nil {
		t.Fatal("Expected NewUserStore to return a store even with invalid JSON")
	}

	// Should have empty users map
	if len(store.users) != 0 {
		t.Error("Expected users map to be empty when JSON is invalid")
	}
}

func TestUserStore_ConcurrentAccess(t *testing.T) {
	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	store := NewUserStore()

	// Test concurrent reads and writes
	var wg sync.WaitGroup
	numGoroutines := 10
	numOperations := 100

	// Concurrent writes
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			for j := 0; j < numOperations; j++ {
				user := &models.User{
					ID:    formatString("user_%d_%d", id, j),
					Email: formatString("<EMAIL>", id, j),
					Name:  formatString("User %d %d", id, j),
				}
				store.SaveUser(user)
			}
		}(i)
	}

	// Concurrent reads
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			for j := 0; j < numOperations; j++ {
				userID := formatString("user_%d_%d", id%numGoroutines, j%numOperations)
				store.GetUser(userID)
			}
		}(i)
	}

	// Concurrent updates
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			for j := 0; j < numOperations; j++ {
				userID := formatString("user_%d_%d", id%numGoroutines, j%numOperations)
				spreadsheetID := formatString("sheet_%d_%d", id, j)
				store.UpdateSpreadsheetID(userID, spreadsheetID)
			}
		}(i)
	}

	// Wait for all goroutines to complete
	done := make(chan bool)
	go func() {
		wg.Wait()
		done <- true
	}()

	select {
	case <-done:
		// Test completed successfully
	case <-time.After(10 * time.Second):
		t.Fatal("Test timed out - possible deadlock")
	}
}

func TestUserStore_SaveToFileError(t *testing.T) {
	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	store := NewUserStore()

	// Make the data directory read-only to cause write errors
	os.Chmod("data", 0444)
	defer os.Chmod("data", 0755) // Restore permissions

	user := &models.User{
		ID:    "test123",
		Email: "<EMAIL>",
		Name:  "Test User",
	}

	// This should not panic, just log a warning
	store.SaveUser(user)

	// User should still be in memory
	retrievedUser, exists := store.GetUser("test123")
	if !exists {
		t.Error("Expected user to be in memory even if file save failed")
	}
	if retrievedUser.Email != user.Email {
		t.Errorf("Expected Email %s, got %s", user.Email, retrievedUser.Email)
	}
}

// Helper function for concurrent test
func formatString(format string, args ...interface{}) string {
	// Simple sprintf implementation for testing
	result := format
	for _, arg := range args {
		switch v := arg.(type) {
		case int:
			result = strings.Replace(result, "%d", strconv.Itoa(v), 1)
		case string:
			result = strings.Replace(result, "%s", v, 1)
		}
	}
	return result
}

func TestUserStore_GetAllUsers(t *testing.T) {
	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	store := NewUserStore()

	// Test with empty store
	users := store.GetAllUsers()
	if len(users) != 0 {
		t.Errorf("Expected 0 users in empty store, got %d", len(users))
	}

	// Add some users
	user1 := &models.User{
		ID:            "user1",
		Email:         "<EMAIL>",
		Name:          "User One",
		SpreadsheetID: "sheet1",
	}
	user2 := &models.User{
		ID:            "user2",
		Email:         "<EMAIL>",
		Name:          "User Two",
		SpreadsheetID: "sheet2",
	}
	user3 := &models.User{
		ID:            "user3",
		Email:         "<EMAIL>",
		Name:          "User Three",
		SpreadsheetID: "",
	}

	store.SaveUser(user1)
	store.SaveUser(user2)
	store.SaveUser(user3)

	// Test getting all users
	users = store.GetAllUsers()
	if len(users) != 3 {
		t.Fatalf("Expected 3 users, got %d", len(users))
	}

	// Verify users are returned (order may vary)
	userMap := make(map[string]*models.User)
	for _, user := range users {
		userMap[user.ID] = user
	}

	// Check user1
	if user, exists := userMap["user1"]; !exists {
		t.Error("Expected user1 to be in results")
	} else {
		if user.Email != "<EMAIL>" {
			t.Errorf("Expected user1 email to be '<EMAIL>', got '%s'", user.Email)
		}
		if user.SpreadsheetID != "sheet1" {
			t.Errorf("Expected user1 SpreadsheetID to be 'sheet1', got '%s'", user.SpreadsheetID)
		}
	}

	// Check user2
	if user, exists := userMap["user2"]; !exists {
		t.Error("Expected user2 to be in results")
	} else {
		if user.Email != "<EMAIL>" {
			t.Errorf("Expected user2 email to be '<EMAIL>', got '%s'", user.Email)
		}
	}

	// Check user3 (with empty SpreadsheetID)
	if user, exists := userMap["user3"]; !exists {
		t.Error("Expected user3 to be in results")
	} else {
		if user.SpreadsheetID != "" {
			t.Errorf("Expected user3 SpreadsheetID to be empty, got '%s'", user.SpreadsheetID)
		}
	}

	// Verify we get copies, not references
	for _, user := range users {
		originalUser, _ := store.GetUser(user.ID)
		if user == originalUser {
			t.Error("Expected GetAllUsers to return copies, not references")
		}
	}
}

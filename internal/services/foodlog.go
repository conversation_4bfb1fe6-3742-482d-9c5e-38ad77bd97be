package services

import (
	"context"
	"fmt"
	"time"

	"github.com/stefanoschrs/mealpal/internal/models"
	"golang.org/x/oauth2"
)

type FoodLogService struct {
	geminiService *GeminiService
	sheetsService *SheetsService
	userStore     UserStoreInterface
}

func NewFoodLogService(geminiService *GeminiService, sheetsService *SheetsService, userStore UserStoreInterface) *FoodLogService {
	return &FoodLogService{
		geminiService: geminiService,
		sheetsService: sheetsService,
		userStore:     userStore,
	}
}

func (s *FoodLogService) LogFood(ctx context.Context, user *models.User, token *oauth2.Token, foodText string) (*models.FoodEntry, error) {
	// Ensure user is stored in our user store
	s.userStore.SaveUser(user)

	// Parse the food text using Gemini
	csvData, err := s.geminiService.ParseFoodText(foodText)
	if err != nil {
		return nil, fmt.Errorf("failed to parse food text: %w", err)
	}

	// Add the parsed data to Google Sheets
	err = s.sheetsService.AddFoodEntry(ctx, user, token, csvData)
	if err != nil {
		return nil, fmt.Errorf("failed to add food entry to sheets: %w", err)
	}

	// Create a food entry record
	entry := &models.FoodEntry{
		UserID:    user.ID,
		Text:      foodText,
		ParsedCSV: csvData,
		Timestamp: time.Now().Format(time.RFC3339),
	}

	return entry, nil
}

func (s *FoodLogService) GetUserStore() UserStoreInterface {
	return s.userStore
}

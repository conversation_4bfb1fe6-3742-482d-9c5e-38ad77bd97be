package services

import (
	"context"
	"crypto/rand"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"time"

	"golang.org/x/oauth2"
)

// SessionService manages Redis-based sessions
type SessionService struct {
	redisService *RedisService
}

// NewSessionService creates a new session service
func NewSessionService(redisService *RedisService) *SessionService {
	return &SessionService{
		redisService: redisService,
	}
}

// SessionData represents the data stored in a session
type SessionData struct {
	UserID       string        `json:"user_id"`
	AccessToken  string        `json:"access_token"`
	RefreshToken string        `json:"refresh_token"`
	TokenExpiry  time.Time     `json:"token_expiry"`
	CreatedAt    time.Time     `json:"created_at"`
	LastAccessed time.Time     `json:"last_accessed"`
}

// CreateSession creates a new session for a user
func (s *SessionService) CreateSession(ctx context.Context, userID string, token *oauth2.Token) (string, error) {
	// Generate session ID
	sessionID, err := generateSessionID()
	if err != nil {
		return "", fmt.Errorf("failed to generate session ID: %w", err)
	}

	// Create session data
	sessionData := SessionData{
		UserID:       userID,
		AccessToken:  token.AccessToken,
		RefreshToken: token.RefreshToken,
		TokenExpiry:  token.Expiry,
		CreatedAt:    time.Now(),
		LastAccessed: time.Now(),
	}

	// Serialize session data
	data, err := json.Marshal(sessionData)
	if err != nil {
		return "", fmt.Errorf("failed to marshal session data: %w", err)
	}

	// Store in Redis with 7 days expiration
	key := fmt.Sprintf("session:%s", sessionID)
	err = s.redisService.Set(ctx, key, data, 7*24*time.Hour)
	if err != nil {
		return "", fmt.Errorf("failed to store session: %w", err)
	}

	// Also store user session mapping for multiple sessions per user
	userSessionKey := fmt.Sprintf("user_sessions:%s", userID)
	err = s.redisService.GetClient().SAdd(ctx, userSessionKey, sessionID).Err()
	if err != nil {
		return "", fmt.Errorf("failed to store user session mapping: %w", err)
	}

	// Set expiration for user session mapping
	err = s.redisService.SetExpiration(ctx, userSessionKey, 7*24*time.Hour)
	if err != nil {
		return "", fmt.Errorf("failed to set expiration for user session mapping: %w", err)
	}

	return sessionID, nil
}

// GetSession retrieves session data by session ID
func (s *SessionService) GetSession(ctx context.Context, sessionID string) (*SessionData, error) {
	key := fmt.Sprintf("session:%s", sessionID)
	data, err := s.redisService.Get(ctx, key)
	if err != nil {
		return nil, fmt.Errorf("session not found: %w", err)
	}

	var sessionData SessionData
	err = json.Unmarshal([]byte(data), &sessionData)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal session data: %w", err)
	}

	// Update last accessed time
	sessionData.LastAccessed = time.Now()
	updatedData, err := json.Marshal(sessionData)
	if err == nil {
		s.redisService.Set(ctx, key, updatedData, 7*24*time.Hour)
	}

	return &sessionData, nil
}

// UpdateSessionTokens updates the OAuth tokens in a session
func (s *SessionService) UpdateSessionTokens(ctx context.Context, sessionID string, token *oauth2.Token) error {
	sessionData, err := s.GetSession(ctx, sessionID)
	if err != nil {
		return fmt.Errorf("failed to get session: %w", err)
	}

	// Update tokens
	sessionData.AccessToken = token.AccessToken
	if token.RefreshToken != "" {
		sessionData.RefreshToken = token.RefreshToken
	}
	sessionData.TokenExpiry = token.Expiry
	sessionData.LastAccessed = time.Now()

	// Serialize and store updated session data
	data, err := json.Marshal(sessionData)
	if err != nil {
		return fmt.Errorf("failed to marshal updated session data: %w", err)
	}

	key := fmt.Sprintf("session:%s", sessionID)
	err = s.redisService.Set(ctx, key, data, 7*24*time.Hour)
	if err != nil {
		return fmt.Errorf("failed to update session: %w", err)
	}

	return nil
}

// DeleteSession removes a session
func (s *SessionService) DeleteSession(ctx context.Context, sessionID string) error {
	// Get session data to find user ID
	sessionData, err := s.GetSession(ctx, sessionID)
	if err != nil {
		// Session doesn't exist, consider it deleted
		return nil
	}

	// Remove from user sessions set
	userSessionKey := fmt.Sprintf("user_sessions:%s", sessionData.UserID)
	s.redisService.GetClient().SRem(ctx, userSessionKey, sessionID)

	// Delete the session
	key := fmt.Sprintf("session:%s", sessionID)
	return s.redisService.Delete(ctx, key)
}

// GetUserSessions returns all active sessions for a user
func (s *SessionService) GetUserSessions(ctx context.Context, userID string) ([]string, error) {
	userSessionKey := fmt.Sprintf("user_sessions:%s", userID)
	return s.redisService.GetClient().SMembers(ctx, userSessionKey).Result()
}

// GetValidTokenForUser returns a valid OAuth token for a user from any of their active sessions
func (s *SessionService) GetValidTokenForUser(ctx context.Context, userID string) (*oauth2.Token, error) {
	sessionIDs, err := s.GetUserSessions(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user sessions: %w", err)
	}

	if len(sessionIDs) == 0 {
		return nil, fmt.Errorf("no active sessions found for user")
	}

	// Try to find a session with a valid token
	for _, sessionID := range sessionIDs {
		sessionData, err := s.GetSession(ctx, sessionID)
		if err != nil {
			continue // Skip invalid sessions
		}

		token := &oauth2.Token{
			AccessToken:  sessionData.AccessToken,
			RefreshToken: sessionData.RefreshToken,
			Expiry:       sessionData.TokenExpiry,
		}

		// Return the first valid token we find
		if token.AccessToken != "" {
			return token, nil
		}
	}

	return nil, fmt.Errorf("no valid tokens found in user sessions")
}

// CleanupExpiredSessions removes expired sessions (should be called periodically)
func (s *SessionService) CleanupExpiredSessions(ctx context.Context) error {
	// Get all session keys
	keys, err := s.redisService.GetKeys(ctx, "session:*")
	if err != nil {
		return fmt.Errorf("failed to get session keys: %w", err)
	}

	for _, key := range keys {
		// Check if key exists (Redis will auto-expire, but we can clean up user mappings)
		exists, err := s.redisService.Exists(ctx, key)
		if err != nil {
			continue
		}

		if !exists {
			// Extract session ID from key
			sessionID := key[8:] // Remove "session:" prefix
			
			// Try to get user sessions to clean up mappings
			userSessionKeys, err := s.redisService.GetKeys(ctx, "user_sessions:*")
			if err != nil {
				continue
			}

			for _, userKey := range userSessionKeys {
				s.redisService.GetClient().SRem(ctx, userKey, sessionID)
			}
		}
	}

	return nil
}

// generateSessionID generates a cryptographically secure session ID
func generateSessionID() (string, error) {
	bytes := make([]byte, 32)
	_, err := rand.Read(bytes)
	if err != nil {
		return "", err
	}
	return base64.URLEncoding.EncodeToString(bytes), nil
}

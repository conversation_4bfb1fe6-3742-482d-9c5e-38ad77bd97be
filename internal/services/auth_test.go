package services

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/stefanoschrs/mealpal/internal/config"
	"golang.org/x/oauth2"
)

func TestNewAuthService(t *testing.T) {
	cfg := &config.Config{
		GoogleClientID:     "test_client_id",
		GoogleClientSecret: "test_client_secret",
		GoogleRedirectURL:  "http://localhost:8080/auth/google/callback",
	}

	authService := NewAuthService(cfg)

	if authService == nil {
		t.Fatal("Expected NewAuthService to return a non-nil service")
	}

	if authService.config != cfg {
		t.Error("Expected config to be set correctly")
	}

	if authService.oauthConfig == nil {
		t.Fatal("Expected oauthConfig to be initialized")
	}

	// Verify OAuth config
	if authService.oauthConfig.ClientID != cfg.GoogleClientID {
		t.Errorf("Expected ClientID %s, got %s", cfg.GoogleClientID, authService.oauthConfig.ClientID)
	}

	if authService.oauthConfig.ClientSecret != cfg.GoogleClientSecret {
		t.Errorf("Expected ClientSecret %s, got %s", cfg.GoogleClientSecret, authService.oauthConfig.ClientSecret)
	}

	if authService.oauthConfig.RedirectURL != cfg.GoogleRedirectURL {
		t.Errorf("Expected RedirectURL %s, got %s", cfg.GoogleRedirectURL, authService.oauthConfig.RedirectURL)
	}

	// Verify scopes
	expectedScopes := []string{
		"https://www.googleapis.com/auth/userinfo.email",
		"https://www.googleapis.com/auth/userinfo.profile",
		"https://www.googleapis.com/auth/drive.file",
	}

	if len(authService.oauthConfig.Scopes) != len(expectedScopes) {
		t.Errorf("Expected %d scopes, got %d", len(expectedScopes), len(authService.oauthConfig.Scopes))
	}

	for i, expectedScope := range expectedScopes {
		if i >= len(authService.oauthConfig.Scopes) || authService.oauthConfig.Scopes[i] != expectedScope {
			t.Errorf("Expected scope %s at index %d, got %s", expectedScope, i, authService.oauthConfig.Scopes[i])
		}
	}
}

func TestAuthService_GetAuthURL(t *testing.T) {
	cfg := &config.Config{
		GoogleClientID:     "test_client_id",
		GoogleClientSecret: "test_client_secret",
		GoogleRedirectURL:  "http://localhost:8080/auth/google/callback",
	}

	authService := NewAuthService(cfg)
	state := "test_state_token"

	authURL := authService.GetAuthURL(state)

	if authURL == "" {
		t.Fatal("Expected GetAuthURL to return a non-empty URL")
	}

	// Verify URL contains expected parameters
	if !strings.Contains(authURL, "accounts.google.com") {
		t.Error("Expected auth URL to contain Google OAuth endpoint")
	}

	if !strings.Contains(authURL, "client_id=test_client_id") {
		t.Error("Expected auth URL to contain client ID")
	}

	if !strings.Contains(authURL, "state=test_state_token") {
		t.Error("Expected auth URL to contain state parameter")
	}

	if !strings.Contains(authURL, "access_type=offline") {
		t.Error("Expected auth URL to contain access_type=offline")
	}
}

func TestAuthService_ExchangeCode(t *testing.T) {
	// Create a mock OAuth server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.URL.Path != "/token" {
			t.Errorf("Expected request to /token, got %s", r.URL.Path)
		}

		if r.Method != "POST" {
			t.Errorf("Expected POST request, got %s", r.Method)
		}

		// Mock successful token response
		response := map[string]interface{}{
			"access_token":  "mock_access_token",
			"refresh_token": "mock_refresh_token",
			"token_type":    "Bearer",
			"expires_in":    3600,
		}

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	}))
	defer server.Close()

	cfg := &config.Config{
		GoogleClientID:     "test_client_id",
		GoogleClientSecret: "test_client_secret",
		GoogleRedirectURL:  "http://localhost:8080/auth/google/callback",
	}

	authService := NewAuthService(cfg)
	// Override the endpoint for testing
	authService.oauthConfig.Endpoint.TokenURL = server.URL + "/token"

	ctx := context.Background()
	token, err := authService.ExchangeCode(ctx, "test_code")

	if err != nil {
		t.Fatalf("Expected ExchangeCode to succeed, got error: %v", err)
	}

	if token == nil {
		t.Fatal("Expected token to be non-nil")
	}

	if token.AccessToken != "mock_access_token" {
		t.Errorf("Expected access token 'mock_access_token', got '%s'", token.AccessToken)
	}

	if token.RefreshToken != "mock_refresh_token" {
		t.Errorf("Expected refresh token 'mock_refresh_token', got '%s'", token.RefreshToken)
	}
}

func TestAuthService_GetUserInfo(t *testing.T) {
	cfg := &config.Config{
		GoogleClientID:     "test_client_id",
		GoogleClientSecret: "test_client_secret",
		GoogleRedirectURL:  "http://localhost:8080/auth/google/callback",
	}

	authService := NewAuthService(cfg)

	// Create a mock token
	token := &oauth2.Token{
		AccessToken:  "mock_access_token",
		RefreshToken: "mock_refresh_token",
		TokenType:    "Bearer",
	}

	ctx := context.Background()

	// This will fail with real Google API, but we can test the error handling
	user, err := authService.GetUserInfo(ctx, token)

	// Should fail with invalid credentials (but sometimes it might succeed in test environment)
	if err != nil {
		// User should be nil on error
		if user != nil {
			t.Error("Expected user to be nil on error")
		}

		// Verify the error contains expected information
		if !strings.Contains(err.Error(), "failed to") {
			t.Errorf("Expected error to contain 'failed to', got: %v", err)
		}
	} else {
		// If it succeeds (unlikely but possible), user should not be nil
		if user == nil {
			t.Error("If GetUserInfo succeeds, user should not be nil")
		}
	}
}

func TestAuthService_GetOAuthConfig(t *testing.T) {
	cfg := &config.Config{
		GoogleClientID:     "test_client_id",
		GoogleClientSecret: "test_client_secret",
		GoogleRedirectURL:  "http://localhost:8080/auth/google/callback",
	}

	authService := NewAuthService(cfg)
	oauthConfig := authService.GetOAuthConfig()

	if oauthConfig == nil {
		t.Fatal("Expected GetOAuthConfig to return a non-nil config")
	}

	if oauthConfig != authService.oauthConfig {
		t.Error("Expected GetOAuthConfig to return the same config instance")
	}
}



# Redis Session Management Setup

This document explains how to set up and use Redis for session management in MealPal.

## Overview

MealPal now uses Redis for session management instead of file-based storage. This provides several benefits:

- **Multiple Sessions**: Users can have multiple active sessions (different devices/browsers)
- **Reliable Token Storage**: Both access and refresh tokens are stored securely in Redis
- **Improved Cron Jobs**: Background jobs can access fresh tokens from any user session
- **Better Scalability**: Redis provides better performance and scalability than file-based storage

## Quick Start

### 1. Start Redis with Docker Compose

```bash
# Copy the environment file and configure Redis password
cp .env.example .env
# Edit .env and set REDIS_PASSWORD to a secure password

# Start Redis
docker-compose up -d redis
```

### 2. Update Environment Variables

Add these variables to your `.env` file:

```bash
# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password-change-this-in-production
REDIS_DB=0
```

### 3. Run the Application

```bash
go run cmd/main.go
```

## Architecture Changes

### Session Storage

**Before (Cookie-based):**
- Session data stored in encrypted cookies
- Limited by cookie size constraints
- Tokens stored in `users.json` file
- Single session per user

**After (Redis-based):**
- Minimal session data in cookies (just session ID)
- Full session data stored in Redis
- Multiple sessions per user supported
- Tokens stored securely in Redis with expiration

### Data Flow

1. **Login**: User authenticates with Google OAuth
2. **Session Creation**: Redis session created with tokens
3. **Cookie Storage**: Minimal session data (session ID) stored in cookie
4. **Request Processing**: Middleware retrieves full session from Redis
5. **Token Refresh**: Updated tokens stored in all user sessions

### Cron Job Improvements

The nightly cron job now:
- Retrieves fresh tokens from Redis sessions
- Handles token refresh automatically
- Updates all user sessions with new tokens
- Continues working even if one session expires

## Redis Data Structure

### Session Keys

```
session:{session_id} -> {
  "user_id": "google_user_id",
  "access_token": "oauth_access_token",
  "refresh_token": "oauth_refresh_token",
  "token_expiry": "2024-01-01T12:00:00Z",
  "created_at": "2024-01-01T10:00:00Z",
  "last_accessed": "2024-01-01T11:30:00Z"
}
```

### User Session Mapping

```
user_sessions:{user_id} -> Set{session_id1, session_id2, ...}
```

## Configuration

### Redis Connection

The application connects to Redis using these environment variables:

- `REDIS_HOST`: Redis server hostname (default: localhost)
- `REDIS_PORT`: Redis server port (default: 6379)
- `REDIS_PORT`: Redis server port (default: 6379)
- `REDIS_PASSWORD`: Redis authentication password
- `REDIS_DB`: Redis database number (default: 0)

### Session Settings

- **Expiration**: Sessions expire after 7 days
- **Cleanup**: Automatic cleanup of expired sessions
- **Multiple Sessions**: Users can have unlimited active sessions

## Development

### Running Tests

```bash
# Start Redis for testing
docker-compose up -d redis

# Run Redis service tests
go test ./internal/services -run TestRedis

# Run session service tests
go test ./internal/services -run TestSession

# Run all tests
go test ./...
```

### Local Development

For local development, you can run Redis without Docker:

```bash
# Install Redis (macOS)
brew install redis

# Start Redis
redis-server

# Test connection
redis-cli ping
```

## Production Deployment

### Security Considerations

1. **Password Protection**: Always set a strong Redis password
2. **Network Security**: Use Redis AUTH and consider network isolation
3. **SSL/TLS**: Consider using Redis with SSL in production
4. **Backup**: Implement Redis backup strategy for session persistence

### Docker Compose Production

```yaml
version: '3.8'
services:
  redis:
    image: redis:7-alpine
    restart: unless-stopped
    command: redis-server --requirepass ${REDIS_PASSWORD} --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - app-network
    # Don't expose port in production - use internal network
```

### Environment Variables

```bash
# Production Redis settings
REDIS_HOST=redis  # Docker service name
REDIS_PORT=6379
REDIS_PASSWORD=very-secure-password-here
REDIS_DB=0
```

## Troubleshooting

### Connection Issues

```bash
# Test Redis connection
redis-cli -h localhost -p 6379 -a your-password ping

# Check Redis logs
docker-compose logs redis
```

### Session Issues

```bash
# List all sessions
redis-cli -a your-password keys "session:*"

# Check user sessions
redis-cli -a your-password smembers "user_sessions:google_user_id"

# View session data
redis-cli -a your-password get "session:session_id"
```

### Migration from File-based Sessions

The application automatically handles the transition:

1. Existing `users.json` data is preserved for spreadsheet IDs
2. Users need to log in again to create Redis sessions
3. Old cookie sessions are ignored
4. Cron jobs will skip users without Redis sessions

## Monitoring

### Redis Metrics

Monitor these Redis metrics:
- Memory usage
- Connected clients
- Commands per second
- Key expiration

### Application Metrics

Monitor these application metrics:
- Session creation rate
- Session lookup failures
- Token refresh frequency
- Cron job success rate

## Backup and Recovery

### Redis Persistence

Redis is configured with AOF (Append Only File) persistence:
- All write operations are logged
- Data survives Redis restarts
- Automatic background rewriting

### Session Recovery

If Redis data is lost:
1. Users will need to log in again
2. Spreadsheet associations are preserved in `users.json`
3. Cron jobs will resume once users re-authenticate

# GoReleaser configuration for MealPal
version: 2

# Project information
project_name: mealpal

# Build configuration
builds:
  - id: linux
    main: ./cmd/main.go
    binary: mealpal
    goos:
      - linux
    goarch:
      - amd64
    env:
      - CGO_ENABLED=0
    ldflags:
      - -s -w
      - -X main.version={{.Version}}
      - -X main.buildTime={{.Date}}
      - -X main.gitCommit={{.ShortCommit}}
    tags:
      - netgo
      - osusergo


package main

import (
	"context"
	"html/template"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/sessions"
	"github.com/stefanoschrs/mealpal/internal/config"
	"github.com/stefanoschrs/mealpal/internal/handlers"
	"github.com/stefanoschrs/mealpal/internal/middleware"
	"github.com/stefanoschrs/mealpal/internal/services"
)

// Build-time variables injected by goreleaser/ldflags
var (
	version   = "dev"
	buildTime = "unknown"
	gitCommit = "unknown"
)

func main() {
	// Load configuration
	cfg := config.Load()

	// Set Gin mode based on environment
	if cfg.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	// Initialize Redis service
	redisService, err := services.NewRedisService(cfg)
	if err != nil {
		log.Fatal("Failed to initialize Redis service:", err)
	}
	defer redisService.Close()

	// Initialize services
	authService := services.NewAuthService(cfg)
	geminiService := services.NewGeminiService(cfg)

	// Initialize Redis-based user store
	redisUserStore := services.NewRedisUserStore(redisService)

	// Migrate existing user data from file store to Redis (one-time migration)
	fileUserStore := services.NewUserStore()
	if err := redisUserStore.MigrateFromFileStore(fileUserStore); err != nil {
		log.Printf("Warning: Failed to migrate user data: %v", err)
	}

	sessionService := services.NewSessionService(redisService)
	sheetsService := services.NewSheetsService(authService.GetOAuthConfig(), redisUserStore)
	foodLogService := services.NewFoodLogService(geminiService, sheetsService, redisUserStore)
	cronjobService := services.NewCronjobService(redisUserStore, sheetsService, authService, sessionService)

	// Initialize session store
	store := sessions.NewCookieStore([]byte(cfg.SessionSecret))
	store.Options = &sessions.Options{
		Path:     "/",
		MaxAge:   86400 * 7, // 7 days
		HttpOnly: true,
		Secure:   cfg.Environment == "production", // Only use secure cookies in production
		SameSite: http.SameSiteLaxMode,
	}

	// Initialize handlers
	authHandler := handlers.NewRedisAuthHandler(authService, store, foodLogService, sessionService)
	appHandler := handlers.NewAppHandler(foodLogService, authService, store, cronjobService, sessionService, redisUserStore)

	// Initialize Gin router
	router := gin.Default()

	// Set up template functions for cache busting
	router.SetFuncMap(template.FuncMap{
		"assetVersion": func(path string) string {
			return path + "?v=" + gitCommit
		},
	})

	// Load HTML templates
	router.LoadHTMLGlob("templates/*")

	// Serve static files
	router.Static("/static", "./static")

	// Serve service worker with version injection at a different path
	router.GET("/sw.js", func(c *gin.Context) {
		c.Header("Content-Type", "application/javascript")
		c.Header("Cache-Control", "no-cache, no-store, must-revalidate")
		c.Header("Pragma", "no-cache")
		c.Header("Expires", "0")
		c.File("./static/js/sw.js")
	})

	// Public routes
	router.GET("/", appHandler.Home)
	router.GET("/error", appHandler.Error)

	// Debug routes (for development/testing)
	router.POST("/debug/trigger-daily-summary", appHandler.DebugTriggerDailySummary)
	router.POST("/debug/trigger-daily-summary/:date", appHandler.DebugTriggerDailySummaryWithDate)

	// Auth routes
	auth := router.Group("/auth")
	{
		auth.GET("/google/login", authHandler.Login)
		auth.GET("/google/callback", authHandler.Callback)
		auth.GET("/logout", authHandler.Logout)
	}

	// Protected routes
	protected := router.Group("/")
	protected.Use(middleware.RedisAuthRequired(store, sessionService, redisUserStore))
	{
		protected.GET("/dashboard", appHandler.Dashboard)
		protected.POST("/api/submit-food", appHandler.SubmitFood)
		protected.GET("/api/spreadsheet-info", appHandler.GetSpreadsheetInfo)
		protected.POST("/api/trigger-daily-summary", appHandler.TriggerDailySummary)
	}

	// Start cronjob service
	if err := cronjobService.Start(); err != nil {
		log.Fatal("Failed to start cronjob service:", err)
	}

	// Create HTTP server
	srv := &http.Server{
		Addr:    ":" + cfg.Port,
		Handler: router,
	}

	// Start server in a goroutine
	go func() {
		log.Printf("Starting MealPal server on port %s", cfg.Port)
		log.Printf("Environment: %s", cfg.Environment)

		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatal("Failed to start server:", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("🛑 Shutting down server...")

	// Stop cronjob service
	cronjobService.Stop()

	// Give outstanding requests a deadline for completion
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		log.Fatal("Server forced to shutdown:", err)
	}

	log.Println("✅ Server exited")
}

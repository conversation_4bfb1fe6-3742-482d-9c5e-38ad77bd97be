# MealPal Cronjob Troubleshooting Guide

## 🔍 Problem Diagnosis

The error you're seeing indicates that OAuth tokens are missing or expired:

```
❌ Failed to process daily summary for user ch<PERSON><PERSON><PERSON><PERSON>@gmail.com: failed to refresh token: failed to refresh token: oauth2: token expired and refresh token is not set
```

## 🛠️ What I Fixed

### 1. **Token Persistence Issue**
- **Problem**: OAuth tokens weren't being saved to the user data file
- **Root Cause**: User model had `json:"-"` tags on token fields
- **Fix**: Changed to `json:"access_token,omitempty"` and `json:"refresh_token,omitempty"`

### 2. **Better Error Handling**
- **Problem**: Cronjob failed completely when one user had token issues
- **Fix**: Added graceful handling for users needing re-authentication
- **Result**: <PERSON>ronjob continues processing other users even if some need re-login

### 3. **Improved Logging**
- **Before**: Generic error messages
- **After**: Clear categorization of issues:
  - ✅ Successful processing
  - ❌ Actual errors
  - ⚠️ Users needing re-authentication

## 🔧 How to Check User Status

Run this command to see which users need attention:

```bash
go run scripts/check-user-tokens.go
```

This will show you:
- Which users have valid tokens
- Which users need to re-authenticate
- The current state of stored user data

## 🚀 How to Fix the Issue

### Option 1: User Re-Authentication (Recommended)
1. **Log out** of MealPal in your browser
2. **Log back in** with Google OAuth
3. This will refresh and properly store your tokens
4. The cronjob will work on the next run

### Option 2: Manual Token Check
```bash
# Check current user status
go run scripts/check-user-tokens.go

# Test the cronjob manually
curl -X POST http://localhost:8080/debug/trigger-daily-summary \
  -H "X-Debug-Key: mealpal-debug-2025"
```

## 📊 Expected Behavior After Fix

### Before Fix:
```
🔄 Starting daily macro summary processing...
👥 Processing daily summaries for 1 users (date: 2025-06-07)
❌ Failed to process daily summary <NAME_EMAIL>: failed to refresh token
📊 Daily summary processing completed: 0 successful, 1 errors
```

### After Fix:
```
🔄 Starting daily macro summary processing...
👥 Processing daily summaries for 1 users (date: 2025-06-07)
⚠️  <NAME_EMAIL>: user needs to log in again (no refresh token available)
📊 Daily summary processing completed: 0 successful, 0 errors, 1 skipped (need re-login)
```

### After Re-Authentication:
```
🔄 Starting daily macro summary processing...
👥 Processing daily summaries for 1 users (date: 2025-06-07)
✅ Successfully processed daily <NAME_EMAIL>
📊 Daily summary processing completed: 1 successful, 0 errors, 0 skipped (need re-login)
```

## 🔐 Why This Happened

1. **OAuth Token Lifecycle**: Google OAuth tokens expire after a certain time
2. **Refresh Token Missing**: The refresh token wasn't being stored properly
3. **Server Restart**: When the server restarts, session data is lost
4. **File Persistence**: Only data marked for JSON serialization gets saved

## 🛡️ Prevention

The fixes I implemented will prevent this issue in the future:

1. **Persistent Token Storage**: Tokens are now saved to the user data file
2. **Automatic Token Refresh**: The cronjob will automatically refresh expired tokens
3. **Graceful Degradation**: Users with token issues are skipped, not failed
4. **Clear Logging**: Easy to identify which users need attention

## 🧪 Testing

After re-authenticating, you can test the cronjob:

```bash
# Manual trigger
curl -X POST http://localhost:8080/debug/trigger-daily-summary \
  -H "X-Debug-Key: mealpal-debug-2025"

# Check logs for success
# Should see: ✅ Successfully processed daily summary for [your-email]
```

## 📝 Next Steps

1. **Re-authenticate**: Log out and log back in to refresh your tokens
2. **Test**: Use the debug endpoint to verify the cronjob works
3. **Monitor**: Check the logs at midnight UTC to see automatic processing
4. **Verify**: Check your Google Sheets for the new "Daily Summary" tab

The cronjob will now work reliably and handle token issues gracefully!

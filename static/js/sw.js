// MealPal Service Worker - PWA functionality

// Cache version will be updated automatically during build
const CACHE_VERSION = 'mealpal-v1';
const CACHE_NAME = `${CACHE_VERSION}-${self.location.search.split('v=')[1] || 'dev'}`;

// Static resources to cache (without version parameters)
const STATIC_CACHE_URLS = [
    '/',
    '/static/css/style.css',
    '/static/js/app.js',
    '/static/manifest.json'
];

// Install event - cache essential resources
self.addEventListener('install', function(event) {
    console.log('Service Worker installing with cache:', CACHE_NAME);
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(function(cache) {
                console.log('Caching essential resources');
                return cache.addAll(STATIC_CACHE_URLS);
            })
            .then(function() {
                console.log('Service Worker installed successfully');
                return self.skipWaiting();
            })
    );
});

// Activate event - clean up old caches and take control
self.addEventListener('activate', function(event) {
    console.log('Service Worker activating...');
    event.waitUntil(
        caches.keys().then(function(cacheNames) {
            return Promise.all(
                cacheNames.map(function(cacheName) {
                    // Delete any cache that doesn't match our current cache name
                    if (cacheName.startsWith('mealpal-v1-') && cacheName !== CACHE_NAME) {
                        console.log('Deleting old cache:', cacheName);
                        return caches.delete(cacheName);
                    }
                })
            );
        }).then(function() {
            console.log('Service Worker activated successfully');
            return self.clients.claim();
        })
    );
});

// Fetch event - network first with cache fallback for static resources
self.addEventListener('fetch', function(event) {
    // For navigation requests, always go to network
    if (event.request.mode === 'navigate') {
        event.respondWith(fetch(event.request));
        return;
    }

    // For static resources, try network first, fallback to cache
    event.respondWith(
        fetch(event.request)
            .then(function(response) {
                // If we got a valid response, update the cache
                if (response.status === 200) {
                    const responseClone = response.clone();
                    caches.open(CACHE_NAME).then(function(cache) {
                        // For versioned assets, strip the version parameter when storing in cache
                        const url = new URL(event.request.url);
                        if (url.searchParams.has('v')) {
                            url.searchParams.delete('v');
                            const cleanRequest = new Request(url.toString(), {
                                method: event.request.method,
                                headers: event.request.headers,
                                body: event.request.body,
                                mode: event.request.mode,
                                credentials: event.request.credentials,
                                cache: event.request.cache,
                                redirect: event.request.redirect,
                                referrer: event.request.referrer
                            });
                            cache.put(cleanRequest, responseClone);
                        } else {
                            cache.put(event.request, responseClone);
                        }
                    });
                }
                return response;
            })
            .catch(function() {
                // Network failed, try cache (strip version parameter for cache lookup)
                const url = new URL(event.request.url);
                if (url.searchParams.has('v')) {
                    url.searchParams.delete('v');
                    const cleanRequest = new Request(url.toString());
                    return caches.match(cleanRequest);
                }
                return caches.match(event.request);
            })
    );
});

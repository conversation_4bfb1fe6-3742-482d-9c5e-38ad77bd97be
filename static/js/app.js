// MealPal Frontend JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // Initialize user dropdown
    initializeUserDropdown();

    // Initialize food form
    initializeFoodForm();

    // Initialize PWA install prompt
    initializePWAInstall();

    // Load spreadsheet info
    loadSpreadsheetInfo();
}

// User Dropdown Functionality
function initializeUserDropdown() {
    const userAvatar = document.getElementById('userAvatar');
    const dropdownMenu = document.getElementById('dropdownMenu');
    
    if (userAvatar && dropdownMenu) {
        userAvatar.addEventListener('click', function(e) {
            e.stopPropagation();
            dropdownMenu.classList.toggle('show');
        });
        
        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!dropdownMenu.contains(e.target) && !userAvatar.contains(e.target)) {
                dropdownMenu.classList.remove('show');
            }
        });
    }
}

// Food Form Functionality
function initializeFoodForm() {
    const foodForm = document.getElementById('foodForm');
    
    if (foodForm) {
        foodForm.addEventListener('submit', handleFoodSubmission);
    }
}

async function handleFoodSubmission(e) {
    e.preventDefault();
    
    const form = e.target;
    const submitBtn = document.getElementById('submitBtn');
    const btnText = submitBtn.querySelector('.btn-text');
    const btnSpinner = submitBtn.querySelector('.btn-spinner');
    const foodTextarea = document.getElementById('foodText');
    
    // Disable form and show loading state
    setFormLoading(true, submitBtn, btnText, btnSpinner);
    
    try {
        const formData = new FormData(form);
        
        const response = await fetch('/api/submit-food', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            showToast('success', result.message || 'Food logged successfully!');
            foodTextarea.value = ''; // Clear the form
            // Refresh spreadsheet info in case this was the first entry
            loadSpreadsheetInfo();
        } else {
            showToast('error', result.error || 'Failed to log food. Please try again.');
        }
    } catch (error) {
        console.error('Error submitting food:', error);
        showToast('error', 'Network error. Please check your connection and try again.');
    } finally {
        // Re-enable form
        setFormLoading(false, submitBtn, btnText, btnSpinner);
    }
}

function setFormLoading(isLoading, submitBtn, btnText, btnSpinner) {
    if (isLoading) {
        submitBtn.disabled = true;
        btnText.style.display = 'none';
        btnSpinner.style.display = 'block';
    } else {
        submitBtn.disabled = false;
        btnText.style.display = 'block';
        btnSpinner.style.display = 'none';
    }
}

// Toast Notification System
function showToast(type, message) {
    const toastContainer = document.getElementById('toast-container');
    
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    
    const messageElement = document.createElement('p');
    messageElement.className = 'toast-message';
    messageElement.textContent = message;
    
    toast.appendChild(messageElement);
    toastContainer.appendChild(toast);
    
    // Auto-remove toast after 5 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 5000);
    
    // Allow manual dismissal by clicking
    toast.addEventListener('click', () => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    });
}

// PWA Installation
let deferredPrompt;

function initializePWAInstall() {
    window.addEventListener('beforeinstallprompt', (e) => {
        // Prevent Chrome 67 and earlier from automatically showing the prompt
        e.preventDefault();
        // Stash the event so it can be triggered later
        deferredPrompt = e;
        
        // Show install button or banner
        showInstallPrompt();
    });
    
    window.addEventListener('appinstalled', (evt) => {
        console.log('PWA was installed');
        hideInstallPrompt();
    });
}

function showInstallPrompt() {
    // Create install prompt if it doesn't exist
    if (!document.getElementById('install-prompt')) {
        const installPrompt = document.createElement('div');
        installPrompt.id = 'install-prompt';
        installPrompt.innerHTML = `
            <div style="
                position: fixed;
                bottom: 20px;
                left: 20px;
                right: 20px;
                background: #4CAF50;
                color: white;
                padding: 16px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                display: flex;
                align-items: center;
                justify-content: space-between;
                z-index: 1000;
                max-width: 400px;
                margin: 0 auto;
            ">
                <span>📱 Install MealPal for quick access!</span>
                <div>
                    <button id="install-btn" style="
                        background: white;
                        color: #4CAF50;
                        border: none;
                        padding: 8px 16px;
                        border-radius: 4px;
                        margin-right: 8px;
                        cursor: pointer;
                        font-weight: 500;
                    ">Install</button>
                    <button id="dismiss-install" style="
                        background: transparent;
                        color: white;
                        border: 1px solid white;
                        padding: 8px 16px;
                        border-radius: 4px;
                        cursor: pointer;
                    ">Later</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(installPrompt);
        
        // Add event listeners
        document.getElementById('install-btn').addEventListener('click', installPWA);
        document.getElementById('dismiss-install').addEventListener('click', hideInstallPrompt);
    }
}

function hideInstallPrompt() {
    const prompt = document.getElementById('install-prompt');
    if (prompt) {
        prompt.remove();
    }
}

async function installPWA() {
    if (deferredPrompt) {
        // Show the prompt
        deferredPrompt.prompt();
        
        // Wait for the user to respond to the prompt
        const { outcome } = await deferredPrompt.userChoice;
        
        console.log(`User response to the install prompt: ${outcome}`);
        
        // Clear the deferredPrompt variable
        deferredPrompt = null;
        
        // Hide the install prompt
        hideInstallPrompt();
    }
}

// Utility Functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Load spreadsheet info and show link if available
async function loadSpreadsheetInfo() {
    try {
        const response = await fetch('/api/spreadsheet-info');
        const result = await response.json();

        if (result.success && result.hasSpreadsheet) {
            const spreadsheetLink = document.getElementById('spreadsheetLink');
            if (spreadsheetLink) {
                spreadsheetLink.href = result.spreadsheetURL;
                spreadsheetLink.style.display = 'flex';
            }
        }
    } catch (error) {
        console.log('Could not load spreadsheet info:', error);
        // Silently fail - this is not critical functionality
    }
}

// Network error handling
function handleNetworkError() {
    showToast('error', 'Network error. Please check your connection and try again.');
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

/* Login Page Styles */
.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
}

.login-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 40px;
    max-width: 400px;
    width: 100%;
    text-align: center;
}

.logo h1 {
    font-size: 2.5rem;
    margin-bottom: 8px;
    color: #4CAF50;
}

.logo p {
    color: #666;
    margin-bottom: 32px;
}

.login-content h2 {
    margin-bottom: 16px;
    color: #333;
}

.login-content p {
    color: #666;
    margin-bottom: 32px;
}

.google-login-btn {
    display: inline-flex;
    align-items: center;
    gap: 12px;
    background: white;
    border: 2px solid #ddd;
    border-radius: 8px;
    padding: 12px 24px;
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: all 0.2s;
    margin-bottom: 32px;
}

.google-login-btn:hover {
    border-color: #4285F4;
    box-shadow: 0 4px 12px rgba(66, 133, 244, 0.2);
}

.features {
    display: flex;
    flex-direction: column;
    gap: 16px;
    text-align: left;
}

.feature {
    display: flex;
    align-items: center;
    gap: 12px;
    color: #666;
}

.feature-icon {
    font-size: 1.2rem;
}

/* App Container Styles */
.app-container {
    min-height: 100vh;
    background: #f8fafc;
}

/* Navigation Styles */
.navbar {
    background: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 16px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-left .logo {
    font-size: 1.5rem;
    color: #4CAF50;
    margin: 0;
}

.user-menu {
    position: relative;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    border: 2px solid #e2e8f0;
    transition: border-color 0.2s;
}

.user-avatar:hover {
    border-color: #4CAF50;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    min-width: 250px;
    z-index: 1000;
    display: none;
    margin-top: 8px;
}

.dropdown-menu.show {
    display: block;
}

.dropdown-header {
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.dropdown-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
}

.dropdown-name {
    font-weight: 600;
    color: #333;
}

.dropdown-email {
    font-size: 0.875rem;
    color: #666;
}

.dropdown-divider {
    height: 1px;
    background: #e2e8f0;
    margin: 0 16px;
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    text-decoration: none;
    color: #333;
    transition: background-color 0.2s;
}

.dropdown-item:hover {
    background: #f8fafc;
}

/* Main Content Styles */
.main-content {
    padding: 32px 24px;
}

.content-container {
    max-width: 800px;
    margin: 0 auto;
}

.welcome-section {
    text-align: center;
    margin-bottom: 40px;
}

.welcome-section h2 {
    font-size: 2rem;
    margin-bottom: 8px;
    color: #333;
}

.welcome-section p {
    color: #666;
    font-size: 1.1rem;
}

/* Food Form Styles */
.food-form-container {
    background: white;
    border-radius: 12px;
    padding: 32px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    margin-bottom: 32px;
}

.form-group {
    margin-bottom: 24px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.form-group textarea {
    width: 100%;
    padding: 16px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-family: inherit;
    font-size: 1rem;
    resize: vertical;
    transition: border-color 0.2s;
}

.form-group textarea:focus {
    outline: none;
    border-color: #4CAF50;
}

.submit-btn {
    background: #4CAF50;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 16px 32px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 120px;
    justify-content: center;
}

.submit-btn:hover:not(:disabled) {
    background: #45a049;
    transform: translateY(-1px);
}

.submit-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.spinner {
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Tips Section */
.tips-section {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.tips-section h3 {
    margin-bottom: 16px;
    color: #333;
}

.tips-list {
    list-style: none;
    padding: 0;
}

.tips-list li {
    padding: 8px 0;
    color: #666;
    position: relative;
    padding-left: 24px;
}

.tips-list li::before {
    content: "•";
    color: #4CAF50;
    position: absolute;
    left: 0;
    font-weight: bold;
}

/* Toast Styles */
#toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
}

.toast {
    background: white;
    border-radius: 8px;
    padding: 16px 20px;
    margin-bottom: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-left: 4px solid;
    max-width: 400px;
    animation: slideIn 0.3s ease-out;
}

.toast.success {
    border-left-color: #4CAF50;
}

.toast.error {
    border-left-color: #f44336;
}

.toast-message {
    margin: 0;
    color: #333;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Error Page Styles */
.error-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
}

.error-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 40px;
    max-width: 500px;
    width: 100%;
    text-align: center;
}

.error-icon {
    font-size: 4rem;
    margin-bottom: 24px;
}

.error-card h1 {
    margin-bottom: 16px;
    color: #333;
}

.error-message {
    color: #666;
    margin-bottom: 32px;
    font-size: 1.1rem;
}

.error-actions {
    display: flex;
    gap: 16px;
    justify-content: center;
}

.btn {
    padding: 12px 24px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-primary {
    background: #4CAF50;
    color: white;
}

.btn-primary:hover {
    background: #45a049;
}

.btn-secondary {
    background: #e2e8f0;
    color: #333;
}

.btn-secondary:hover {
    background: #cbd5e0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .navbar {
        padding: 12px 16px;
    }

    .main-content {
        padding: 20px 16px;
    }

    .food-form-container {
        padding: 20px;
    }

    .login-card {
        padding: 24px;
    }

    .error-actions {
        flex-direction: column;
    }

    .dropdown-menu {
        right: -8px;
        min-width: 200px;
    }
}

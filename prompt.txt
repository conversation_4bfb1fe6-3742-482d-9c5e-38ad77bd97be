I want to create a web application AI assistant for daily food logging with the following specifications:

**Frontend Requirements:**
- Progressive Web App (PWA) with manifest.json for Android home screen installation
- Home page: Google OAuth login only (no other content)
- Authenticated user interface:
  - Navigation bar with "MealPal" logo/text (left) and user avatar with logout dropdown (right)
  - Main content area with a textarea for food input and submit button
  - Toast notifications: green for successful submissions, red for errors with specific error messages
  - Form should reset/clear after successful submission

**Backend Architecture (Go):**
- Follow Go project structure best practices:
  - `cmd/` directory for main application entry point
  - `internal/` directory for private application code
  - `lib/` or `pkg/` directory for reusable packages
- Use Gin web framework (github.com/gin-gonic/gin)
- Use Go's built-in html/template package for server-side rendering
- Implement Google OAuth 2.0 for authentication

**Core Functionality:**
- POST endpoint for food log submissions
- Integration with Google Gemini API to:
  - Parse and analyze user's food input text
  - Return structured data in CSV format
- Google Sheets API integration to:
  - Create new rows in a spreadsheet on the user's Google Drive
  - Handle authentication/authorization for Google Drive access

**Technical Details Needed:**
- Session management for authenticated users
- Error handling for API failures (Gemini, Google Sheets)
- Rate limiting considerations
- Environment configuration for API keys
- HTTPS setup for production (required for PWA and OAuth)

Please implement this step by step, starting with the basic Go project structure and Gin setup.

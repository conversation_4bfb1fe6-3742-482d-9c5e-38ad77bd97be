<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.title}}</title>

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#4CAF50">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="MealPal">

    <!-- PWA Icons -->
    <link rel="icon" href="/static/favicon.ico" type="image/x-icon">
    <link rel="apple-touch-icon" href="/static/icons/icon-192x192.png">
    <link rel="icon" type="image/png" sizes="192x192" href="/static/icons/icon-192x192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="/static/icons/icon-512x512.png">

    <!-- <PERSON><PERSON> Manifest -->
    <link rel="manifest" href="{{assetVersion "/static/manifest.json"}}">

    <!-- CSS -->
    <link rel="stylesheet" href="{{assetVersion "/static/css/style.css"}}">

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="error-container">
        <div class="error-card">
            <div class="error-icon">❌</div>
            <h1>Oops! Something went wrong</h1>
            <p class="error-message">{{.error}}</p>
            <div class="error-actions">
                <a href="/" class="btn btn-primary">Go Home</a>
                <button onclick="history.back()" class="btn btn-secondary">Go Back</button>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div id="toast-container"></div>

    <!-- JavaScript -->
    <script src="{{assetVersion "/static/js/app.js"}}"></script>

    <!-- PWA Service Worker -->
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                        console.log('ServiceWorker registration successful');
                    })
                    .catch(function(err) {
                        console.log('ServiceWorker registration failed: ', err);
                    });
            });
        }
    </script>
</body>
</html>
